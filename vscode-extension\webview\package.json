{"name": "aetherforge-webview", "version": "1.0.0", "description": "React webview components for Aetherforge VS Code Extension", "main": "dist/index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@types/d3": "^7.4.3", "d3": "^7.9.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "html2canvas": "^1.4.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "react-virtualized": "^9.22.6", "react-window": "^1.8.11", "recharts": "^2.8.0", "vis-data": "^7.1.9", "vis-network": "^9.1.12", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.23.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.7.0", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "typescript": "^5.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.0"}}