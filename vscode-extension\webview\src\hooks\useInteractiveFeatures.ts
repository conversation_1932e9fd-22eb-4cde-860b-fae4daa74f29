import { useContext, createContext, useCallback } from 'react';

// Interactive Features Context
interface InteractiveFeaturesContextType {
  // Notifications
  addNotification: (notification: {
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    actions?: Array<{
      label: string;
      action: () => void;
      style?: 'primary' | 'secondary' | 'danger';
    }>;
    persistent?: boolean;
  }) => string;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;

  // Progress Indicators
  addProgressIndicator: (indicator: {
    label: string;
    progress: number;
    status: 'active' | 'completed' | 'error' | 'paused';
    estimatedTime?: number;
    details?: string;
    subTasks?: Array<{
      id: string;
      label: string;
      progress: number;
      status: string;
    }>;
  }) => string;
  updateProgressIndicator: (id: string, updates: {
    progress?: number;
    status?: 'active' | 'completed' | 'error' | 'paused';
    estimatedTime?: number;
    details?: string;
    subTasks?: Array<{
      id: string;
      label: string;
      progress: number;
      status: string;
    }>;
  }) => void;
  removeProgressIndicator: (id: string) => void;

  // Real-time Updates
  addRealTimeUpdate: (update: {
    type: 'project_update' | 'agent_status' | 'workflow_progress' | 'system_alert' | 'user_action';
    data: any;
    source: string;
  }) => string;

  // Drag and Drop
  handleDragStart: (item: {
    id: string;
    type: string;
    data: any;
    preview?: React.ReactNode;
  }) => void;
  handleDragEnd: () => void;
  registerDropZone: (zone: {
    id: string;
    accepts: string[];
    onDrop: (item: any) => void;
  }) => void;
  unregisterDropZone: (id: string) => void;

  // Sound Effects
  playSound: (type: 'success' | 'error' | 'warning' | 'info') => void;
  setSoundEnabled: (enabled: boolean) => void;

  // Settings
  setNotificationsEnabled: (enabled: boolean) => void;
  setRealTimeEnabled: (enabled: boolean) => void;
}

const InteractiveFeaturesContext = createContext<InteractiveFeaturesContextType | null>(null);

// Hook to use interactive features
export const useInteractiveFeatures = () => {
  const context = useContext(InteractiveFeaturesContext);
  if (!context) {
    throw new Error('useInteractiveFeatures must be used within an InteractiveFeaturesProvider');
  }
  return context;
};

// Convenience hooks for specific features
export const useNotifications = () => {
  const { addNotification, removeNotification, clearAllNotifications } = useInteractiveFeatures();
  
  const showSuccess = useCallback((title: string, message: string, duration = 3000) => {
    return addNotification({ type: 'success', title, message, duration });
  }, [addNotification]);

  const showError = useCallback((title: string, message: string, duration = 5000) => {
    return addNotification({ type: 'error', title, message, duration });
  }, [addNotification]);

  const showWarning = useCallback((title: string, message: string, duration = 4000) => {
    return addNotification({ type: 'warning', title, message, duration });
  }, [addNotification]);

  const showInfo = useCallback((title: string, message: string, duration = 3000) => {
    return addNotification({ type: 'info', title, message, duration });
  }, [addNotification]);

  const showConfirmation = useCallback((
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void
  ) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      persistent: true,
      actions: [
        {
          label: 'Confirm',
          action: onConfirm,
          style: 'primary'
        },
        {
          label: 'Cancel',
          action: onCancel || (() => {}),
          style: 'secondary'
        }
      ]
    });
  }, [addNotification]);

  return {
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation
  };
};

export const useProgress = () => {
  const { addProgressIndicator, updateProgressIndicator, removeProgressIndicator } = useInteractiveFeatures();

  const createProgress = useCallback((label: string, estimatedTime?: number) => {
    return addProgressIndicator({
      label,
      progress: 0,
      status: 'active',
      estimatedTime
    });
  }, [addProgressIndicator]);

  const updateProgress = useCallback((id: string, progress: number, details?: string) => {
    updateProgressIndicator(id, { progress, details });
  }, [updateProgressIndicator]);

  const completeProgress = useCallback((id: string, details?: string) => {
    updateProgressIndicator(id, { progress: 100, status: 'completed', details });
    // Auto-remove after 3 seconds
    setTimeout(() => removeProgressIndicator(id), 3000);
  }, [updateProgressIndicator, removeProgressIndicator]);

  const errorProgress = useCallback((id: string, details?: string) => {
    updateProgressIndicator(id, { status: 'error', details });
  }, [updateProgressIndicator]);

  const pauseProgress = useCallback((id: string, details?: string) => {
    updateProgressIndicator(id, { status: 'paused', details });
  }, [updateProgressIndicator]);

  const resumeProgress = useCallback((id: string, details?: string) => {
    updateProgressIndicator(id, { status: 'active', details });
  }, [updateProgressIndicator]);

  return {
    createProgress,
    updateProgress,
    completeProgress,
    errorProgress,
    pauseProgress,
    resumeProgress,
    removeProgress: removeProgressIndicator
  };
};

export const useDragDrop = () => {
  const { handleDragStart, handleDragEnd, registerDropZone, unregisterDropZone } = useInteractiveFeatures();

  const createDraggable = useCallback((
    element: HTMLElement,
    item: {
      id: string;
      type: string;
      data: any;
      preview?: React.ReactNode;
    }
  ) => {
    element.draggable = true;
    
    const handleDragStartEvent = (e: DragEvent) => {
      handleDragStart(item);
      e.dataTransfer?.setData('text/plain', JSON.stringify(item));
    };

    const handleDragEndEvent = () => {
      handleDragEnd();
    };

    element.addEventListener('dragstart', handleDragStartEvent);
    element.addEventListener('dragend', handleDragEndEvent);

    return () => {
      element.removeEventListener('dragstart', handleDragStartEvent);
      element.removeEventListener('dragend', handleDragEndEvent);
    };
  }, [handleDragStart, handleDragEnd]);

  const createDropZone = useCallback((
    element: HTMLElement,
    zone: {
      id: string;
      accepts: string[];
      onDrop: (item: any) => void;
    }
  ) => {
    registerDropZone(zone);

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      element.classList.add('drag-over');
    };

    const handleDragLeave = () => {
      element.classList.remove('drag-over');
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      element.classList.remove('drag-over');
      
      const data = e.dataTransfer?.getData('text/plain');
      if (data) {
        try {
          const item = JSON.parse(data);
          if (zone.accepts.includes(item.type)) {
            zone.onDrop(item);
          }
        } catch (error) {
          console.error('Failed to parse drag data:', error);
        }
      }
    };

    element.addEventListener('dragover', handleDragOver);
    element.addEventListener('dragleave', handleDragLeave);
    element.addEventListener('drop', handleDrop);

    return () => {
      element.removeEventListener('dragover', handleDragOver);
      element.removeEventListener('dragleave', handleDragLeave);
      element.removeEventListener('drop', handleDrop);
      unregisterDropZone(zone.id);
    };
  }, [registerDropZone, unregisterDropZone]);

  return {
    createDraggable,
    createDropZone
  };
};

export const useRealTimeUpdates = () => {
  const { addRealTimeUpdate } = useInteractiveFeatures();

  const sendUpdate = useCallback((
    type: 'project_update' | 'agent_status' | 'workflow_progress' | 'system_alert' | 'user_action',
    data: any,
    source = 'user'
  ) => {
    return addRealTimeUpdate({ type, data, source });
  }, [addRealTimeUpdate]);

  const sendProjectUpdate = useCallback((projectId: string, update: any) => {
    return sendUpdate('project_update', { projectId, ...update }, 'project');
  }, [sendUpdate]);

  const sendAgentStatus = useCallback((agentId: string, status: any) => {
    return sendUpdate('agent_status', { agentId, ...status }, 'agent');
  }, [sendUpdate]);

  const sendWorkflowProgress = useCallback((workflowId: string, progress: any) => {
    return sendUpdate('workflow_progress', { workflowId, ...progress }, 'workflow');
  }, [sendUpdate]);

  const sendSystemAlert = useCallback((alert: any) => {
    return sendUpdate('system_alert', alert, 'system');
  }, [sendUpdate]);

  return {
    sendUpdate,
    sendProjectUpdate,
    sendAgentStatus,
    sendWorkflowProgress,
    sendSystemAlert
  };
};

export const useKeyboardShortcuts = () => {
  const registerShortcut = useCallback((
    key: string,
    action: () => void,
    options: {
      ctrlKey?: boolean;
      shiftKey?: boolean;
      altKey?: boolean;
      description?: string;
      category?: string;
    } = {}
  ) => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key.toLowerCase() === key.toLowerCase() &&
        !!options.ctrlKey === event.ctrlKey &&
        !!options.shiftKey === event.shiftKey &&
        !!options.altKey === event.altKey
      ) {
        event.preventDefault();
        action();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return { registerShortcut };
};

export { InteractiveFeaturesContext };
