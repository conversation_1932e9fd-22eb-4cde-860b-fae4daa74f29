# pheromone_bus_simple.py - Simple working pheromone communication system

import json
import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

PHEROMONE_FILE = "pheromones.json"
MAX_PHEROMONES = 1000

def load_pheromones() -> List[Dict[str, Any]]:
    """Load pheromones from persistent storage"""
    if not os.path.exists(PHEROMONE_FILE):
        return []
    
    try:
        with open(PHEROMONE_FILE, "r") as f:
            data = json.load(f)
            return data if isinstance(data, list) else []
    except (json.JSONDecodeError, IOError):
        return []

def save_pheromones(pheromones: List[Dict[str, Any]]):
    """Save pheromones to persistent storage"""
    try:
        # Ensure directory exists
        Path(PHEROMONE_FILE).parent.mkdir(parents=True, exist_ok=True)
        
        with open(PHEROMONE_FILE, "w") as f:
            json.dump(pheromones, f, indent=2)
    except IOError:
        pass  # Fail silently for now

def drop_pheromone(signal: str, payload: Dict[str, Any],
                  project_id: Optional[str] = None,
                  agent_id: Optional[str] = None,
                  trail_id: Optional[str] = None,
                  priority: int = 5,
                  ttl_seconds: Optional[int] = None,
                  tags: Optional[List[str]] = None,
                  metadata: Optional[Dict[str, Any]] = None) -> str:
    """
    Enhanced pheromone dropping with expiration, priority levels, and metadata

    Args:
        signal: The signal type
        payload: The payload data
        project_id: Associated project ID
        agent_id: Agent that dropped the pheromone
        trail_id: Trail ID for grouping related pheromones
        priority: Priority level (1-10, higher is more important)
        ttl_seconds: Time to live in seconds (None for no expiration)
        tags: List of tags for categorization and filtering
        metadata: Additional metadata for the pheromone

    Returns:
        The unique pheromone ID
    """
    pheromone_id = str(uuid.uuid4())
    timestamp = datetime.now()

    # Calculate expiration time if TTL is provided
    expires_at = None
    if ttl_seconds is not None:
        expires_at = (timestamp + timedelta(seconds=ttl_seconds)).isoformat()

    # Determine default TTL based on signal type
    if ttl_seconds is None:
        default_ttls = {
            "error": 3600,  # 1 hour
            "warning": 1800,  # 30 minutes
            "info": 900,  # 15 minutes
            "debug": 300,  # 5 minutes
            "progress": 600,  # 10 minutes
            "completion": 7200,  # 2 hours
            "failure": 3600,  # 1 hour
        }

        # Extract signal category from signal name
        signal_category = signal.split('_')[0] if '_' in signal else signal
        default_ttl = default_ttls.get(signal_category, 1800)  # Default 30 minutes
        expires_at = (timestamp + timedelta(seconds=default_ttl)).isoformat()

    pheromone = {
        "id": pheromone_id,
        "timestamp": timestamp.isoformat(),
        "expires_at": expires_at,
        "signal": signal,
        "payload": payload,
        "project_id": project_id,
        "agent_id": agent_id,
        "trail_id": trail_id,
        "priority": priority,
        "tags": tags or [],
        "metadata": metadata or {},
        "strength": calculate_pheromone_strength(priority, signal),
        "decay_rate": calculate_decay_rate(signal, priority)
    }

    # Load existing pheromones and clean expired ones
    pheromones = load_pheromones()
    pheromones = cleanup_expired_pheromones(pheromones)

    # Insert pheromone in priority order
    inserted = False
    for i, existing in enumerate(pheromones):
        if existing.get("priority", 5) < priority:
            pheromones.insert(i, pheromone)
            inserted = True
            break

    if not inserted:
        pheromones.append(pheromone)

    # Limit the number of pheromones
    if len(pheromones) > MAX_PHEROMONES:
        # Keep highest priority pheromones
        pheromones.sort(key=lambda x: x.get("priority", 5), reverse=True)
        pheromones = pheromones[:MAX_PHEROMONES]

    # Save to persistent storage
    save_pheromones(pheromones)

    # Enhanced logging with priority and metadata
    priority_indicator = "🔴" if priority >= 8 else "🟡" if priority >= 6 else "🟢"
    print(f"[Pheromone {priority_indicator}] {signal} (P:{priority}) → {payload}")

    return pheromone_id

def calculate_pheromone_strength(priority: int, signal: str) -> float:
    """Calculate initial pheromone strength based on priority and signal type"""
    base_strength = priority / 10.0  # Normalize to 0-1

    # Signal type modifiers
    signal_modifiers = {
        "error": 1.5,
        "failure": 1.4,
        "warning": 1.2,
        "completion": 1.3,
        "progress": 1.0,
        "info": 0.8,
        "debug": 0.6
    }

    signal_category = signal.split('_')[0] if '_' in signal else signal
    modifier = signal_modifiers.get(signal_category, 1.0)

    return min(base_strength * modifier, 1.0)

def calculate_decay_rate(signal: str, priority: int) -> float:
    """Calculate decay rate for pheromone strength over time"""
    # Higher priority pheromones decay slower
    base_decay = 0.1 - (priority * 0.01)  # 0.01 to 0.09

    # Signal type affects decay rate
    signal_decay_modifiers = {
        "error": 0.5,  # Errors decay slower
        "failure": 0.6,
        "completion": 0.3,  # Completions decay very slowly
        "progress": 1.2,  # Progress decays faster
        "debug": 2.0  # Debug info decays quickly
    }

    signal_category = signal.split('_')[0] if '_' in signal else signal
    modifier = signal_decay_modifiers.get(signal_category, 1.0)

    return max(base_decay * modifier, 0.01)  # Minimum decay rate

def cleanup_expired_pheromones(pheromones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove expired pheromones and update strength based on decay"""
    current_time = datetime.now()
    active_pheromones = []

    for pheromone in pheromones:
        # Check if pheromone has expired
        expires_at = pheromone.get("expires_at")
        if expires_at:
            try:
                expiry_time = datetime.fromisoformat(expires_at)
                if current_time > expiry_time:
                    continue  # Skip expired pheromone
            except ValueError:
                pass  # Invalid timestamp, keep pheromone

        # Update strength based on decay
        try:
            timestamp = datetime.fromisoformat(pheromone.get("timestamp", current_time.isoformat()))
            age_seconds = (current_time - timestamp).total_seconds()

            original_strength = pheromone.get("strength", 1.0)
            decay_rate = pheromone.get("decay_rate", 0.1)

            # Calculate current strength using exponential decay
            current_strength = original_strength * (0.5 ** (age_seconds * decay_rate / 3600))  # Half-life in hours

            # Keep pheromone if strength is still significant
            if current_strength > 0.01:  # 1% threshold
                pheromone["current_strength"] = current_strength
                active_pheromones.append(pheromone)
        except (ValueError, TypeError):
            # If there's an error with timestamp or calculations, keep the pheromone
            active_pheromones.append(pheromone)

    return active_pheromones

def get_pheromones(signal: Optional[str] = None,
                  project_id: Optional[str] = None,
                  agent_id: Optional[str] = None,
                  trail_id: Optional[str] = None,
                  limit: Optional[int] = None,
                  signal_pattern: Optional[str] = None,
                  since: Optional[str] = None,
                  priority_min: Optional[int] = None,
                  priority_max: Optional[int] = None,
                  tags: Optional[List[str]] = None,
                  include_expired: bool = False,
                  sort_by: str = "timestamp",
                  sort_order: str = "desc") -> List[Dict[str, Any]]:
    """
    Enhanced pheromone retrieval with advanced filtering and sorting

    Args:
        signal: Filter by exact signal type
        project_id: Filter by project ID
        agent_id: Filter by agent ID
        trail_id: Filter by trail ID
        limit: Maximum number of pheromones to return
        signal_pattern: Filter by signal pattern (supports wildcards)
        since: ISO timestamp to filter pheromones since
        priority_min: Minimum priority level (inclusive)
        priority_max: Maximum priority level (inclusive)
        tags: Filter by tags (pheromone must have all specified tags)
        include_expired: Whether to include expired pheromones
        sort_by: Sort field (timestamp, priority, strength)
        sort_order: Sort order (asc, desc)

    Returns:
        List of matching pheromones with current strength and metadata
    """
    pheromones = load_pheromones()

    # Clean expired pheromones unless explicitly requested
    if not include_expired:
        pheromones = cleanup_expired_pheromones(pheromones)

    # Apply filters
    if signal:
        pheromones = [p for p in pheromones if p.get("signal") == signal]

    if signal_pattern:
        import fnmatch
        pheromones = [p for p in pheromones if fnmatch.fnmatch(p.get("signal", ""), signal_pattern)]

    if project_id:
        pheromones = [p for p in pheromones if p.get("project_id") == project_id]

    if agent_id:
        pheromones = [p for p in pheromones if p.get("agent_id") == agent_id]

    if trail_id:
        pheromones = [p for p in pheromones if p.get("trail_id") == trail_id]

    if since:
        try:
            since_dt = datetime.fromisoformat(since.replace('Z', '+00:00'))
            pheromones = [p for p in pheromones
                         if datetime.fromisoformat(p.get("timestamp", "")) >= since_dt]
        except ValueError:
            pass  # Invalid timestamp format, skip filter

    if priority_min is not None:
        pheromones = [p for p in pheromones if p.get("priority", 5) >= priority_min]

    if priority_max is not None:
        pheromones = [p for p in pheromones if p.get("priority", 5) <= priority_max]

    if tags:
        pheromones = [p for p in pheromones
                     if all(tag in p.get("tags", []) for tag in tags)]

    # Sort pheromones
    sort_key_map = {
        "timestamp": lambda x: x.get("timestamp", ""),
        "priority": lambda x: x.get("priority", 5),
        "strength": lambda x: x.get("current_strength", x.get("strength", 1.0))
    }

    if sort_by in sort_key_map:
        reverse = sort_order.lower() == "desc"
        pheromones.sort(key=sort_key_map[sort_by], reverse=reverse)

    # Apply limit
    if limit:
        pheromones = pheromones[:limit]

    return pheromones

def get_statistics() -> Dict[str, Any]:
    """Get pheromone bus statistics"""
    pheromones = load_pheromones()
    
    # Count by signal type
    signal_counts = {}
    project_counts = {}
    agent_counts = {}
    
    for pheromone in pheromones:
        signal = pheromone.get("signal", "unknown")
        project_id = pheromone.get("project_id")
        agent_id = pheromone.get("agent_id")
        
        signal_counts[signal] = signal_counts.get(signal, 0) + 1
        
        if project_id:
            project_counts[project_id] = project_counts.get(project_id, 0) + 1
        
        if agent_id:
            agent_counts[agent_id] = agent_counts.get(agent_id, 0) + 1
    
    return {
        "total_pheromones": len(pheromones),
        "signal_counts": signal_counts,
        "project_counts": project_counts,
        "agent_counts": agent_counts,
        "last_updated": datetime.now().isoformat()
    }

def cleanup_old_pheromones(max_age_hours: int = 24):
    """Remove old pheromones"""
    pheromones = load_pheromones()
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    
    # Filter out old pheromones
    active_pheromones = []
    for pheromone in pheromones:
        timestamp_str = pheromone.get("timestamp")
        if timestamp_str:
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
                if timestamp > cutoff_time:
                    active_pheromones.append(pheromone)
            except ValueError:
                # Keep pheromones with invalid timestamps
                active_pheromones.append(pheromone)
        else:
            # Keep pheromones without timestamps
            active_pheromones.append(pheromone)
    
    if len(active_pheromones) < len(pheromones):
        save_pheromones(active_pheromones)
        print(f"Cleaned up {len(pheromones) - len(active_pheromones)} old pheromones")

def get_pheromone_trails(project_id: Optional[str] = None,
                        agent_id: Optional[str] = None,
                        limit: Optional[int] = None) -> Dict[str, List[Dict[str, Any]]]:
    """Get pheromone trails grouped by trail_id for visualization"""
    pheromones = get_pheromones(
        project_id=project_id,
        agent_id=agent_id,
        limit=limit,
        sort_by="timestamp",
        sort_order="asc"
    )

    trails = {}
    for pheromone in pheromones:
        trail_id = pheromone.get("trail_id", "default")
        if trail_id not in trails:
            trails[trail_id] = []
        trails[trail_id].append(pheromone)

    return trails

def get_pheromone_network(project_id: Optional[str] = None) -> Dict[str, Any]:
    """Generate network data for pheromone visualization"""
    pheromones = get_pheromones(project_id=project_id)

    nodes = {}
    edges = []

    for pheromone in pheromones:
        agent_id = pheromone.get("agent_id", "unknown")
        signal = pheromone.get("signal", "unknown")

        # Create agent node
        if agent_id not in nodes:
            nodes[agent_id] = {
                "id": agent_id,
                "type": "agent",
                "label": agent_id,
                "pheromone_count": 0,
                "total_strength": 0.0
            }

        nodes[agent_id]["pheromone_count"] += 1
        nodes[agent_id]["total_strength"] += pheromone.get("current_strength", 1.0)

        # Create signal node
        signal_node_id = f"signal_{signal}"
        if signal_node_id not in nodes:
            nodes[signal_node_id] = {
                "id": signal_node_id,
                "type": "signal",
                "label": signal,
                "pheromone_count": 0,
                "total_strength": 0.0
            }

        nodes[signal_node_id]["pheromone_count"] += 1
        nodes[signal_node_id]["total_strength"] += pheromone.get("current_strength", 1.0)

        # Create edge
        edges.append({
            "from": agent_id,
            "to": signal_node_id,
            "weight": pheromone.get("current_strength", 1.0),
            "priority": pheromone.get("priority", 5),
            "timestamp": pheromone.get("timestamp")
        })

    return {
        "nodes": list(nodes.values()),
        "edges": edges
    }

def get_pheromone_heatmap(project_id: Optional[str] = None,
                         time_window_hours: int = 24) -> Dict[str, Any]:
    """Generate heatmap data for pheromone activity over time"""
    since = (datetime.now() - timedelta(hours=time_window_hours)).isoformat()
    pheromones = get_pheromones(
        project_id=project_id,
        since=since,
        sort_by="timestamp",
        sort_order="asc"
    )

    # Group by hour and signal type
    heatmap_data = {}
    for pheromone in pheromones:
        try:
            timestamp = datetime.fromisoformat(pheromone.get("timestamp", ""))
            hour_key = timestamp.strftime("%Y-%m-%d %H:00")
            signal = pheromone.get("signal", "unknown")

            if hour_key not in heatmap_data:
                heatmap_data[hour_key] = {}

            if signal not in heatmap_data[hour_key]:
                heatmap_data[hour_key][signal] = {
                    "count": 0,
                    "total_strength": 0.0,
                    "avg_priority": 0.0,
                    "priorities": []
                }

            heatmap_data[hour_key][signal]["count"] += 1
            heatmap_data[hour_key][signal]["total_strength"] += pheromone.get("current_strength", 1.0)
            heatmap_data[hour_key][signal]["priorities"].append(pheromone.get("priority", 5))
        except ValueError:
            continue  # Skip invalid timestamps

    # Calculate averages
    for hour_data in heatmap_data.values():
        for signal_data in hour_data.values():
            if signal_data["priorities"]:
                signal_data["avg_priority"] = sum(signal_data["priorities"]) / len(signal_data["priorities"])
            del signal_data["priorities"]  # Remove raw data

    return heatmap_data

# Test function
if __name__ == "__main__":
    print("Testing simple pheromone bus...")
    
    # Test dropping a pheromone
    pheromone_id = drop_pheromone("test_signal", {"message": "Hello, world!"}, project_id="test_project")
    print(f"Dropped pheromone: {pheromone_id}")
    
    # Test getting pheromones
    pheromones = get_pheromones()
    print(f"Total pheromones: {len(pheromones)}")
    
    # Test statistics
    stats = get_statistics()
    print(f"Statistics: {stats}")
    
    print("Simple pheromone bus test completed successfully!")
