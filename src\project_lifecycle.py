"""
Comprehensive Project Lifecycle Management for Aetherforge
Handles project archiving, resumption, templates, configuration validation, and metadata tracking
"""

import json
import shutil
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import logging
from pydantic import BaseModel, Field, validator
import yaml
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class ProjectMetadata:
    """Comprehensive project metadata"""
    project_id: str
    name: str
    description: str
    created_at: str
    updated_at: str
    version: str
    status: str  # active, archived, suspended, completed, failed
    template_id: Optional[str]
    tags: List[str]
    owner: str
    collaborators: List[str]
    workflow_type: str
    technology_stack: List[str]
    estimated_duration: Optional[int]  # in hours
    actual_duration: Optional[int]  # in hours
    completion_percentage: float
    file_count: int
    total_size_bytes: int
    last_activity: str
    checksum: str
    custom_fields: Dict[str, Any]

@dataclass
class ProjectTemplate:
    """Project template definition"""
    template_id: str
    name: str
    description: str
    category: str
    version: str
    created_at: str
    author: str
    tags: List[str]
    workflow_type: str
    technology_stack: List[str]
    estimated_duration: int
    difficulty_level: str  # beginner, intermediate, advanced
    file_structure: Dict[str, Any]
    configuration: Dict[str, Any]
    dependencies: List[str]
    post_creation_steps: List[str]
    documentation_links: List[str]

class ProjectConfiguration(BaseModel):
    """Project configuration validation model"""
    project_name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., max_length=1000)
    workflow_type: str = Field(..., regex=r'^(greenfield|brownfield|microservice|data_pipeline|ml_project)$')
    technology_stack: List[str] = Field(..., min_items=1)
    estimated_duration: Optional[int] = Field(None, ge=1, le=10000)
    template_id: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    custom_configuration: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('technology_stack')
    def validate_tech_stack(cls, v):
        valid_technologies = {
            'python', 'javascript', 'typescript', 'react', 'vue', 'angular',
            'node.js', 'express', 'fastapi', 'django', 'flask', 'spring',
            'java', 'c#', 'go', 'rust', 'php', 'ruby', 'swift', 'kotlin',
            'postgresql', 'mysql', 'mongodb', 'redis', 'elasticsearch',
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'terraform'
        }
        
        for tech in v:
            if tech.lower() not in valid_technologies:
                logger.warning(f"Unknown technology: {tech}")
        
        return v

class ProjectLifecycleManager:
    """Comprehensive project lifecycle management"""
    
    def __init__(self, base_path: str = "./projects", templates_path: str = "./templates"):
        self.base_path = Path(base_path)
        self.templates_path = Path(templates_path)
        self.archive_path = self.base_path / "archived"
        self.metadata_path = self.base_path / "metadata"
        
        # Create directories
        for path in [self.base_path, self.templates_path, self.archive_path, self.metadata_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        self.active_projects = {}
        self.templates = {}
        self._load_existing_projects()
        self._load_templates()
    
    def _load_existing_projects(self):
        """Load existing project metadata"""
        try:
            for metadata_file in self.metadata_path.glob("*.json"):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    self.active_projects[metadata['project_id']] = ProjectMetadata(**metadata)
        except Exception as e:
            logger.error(f"Failed to load existing projects: {e}")
    
    def _load_templates(self):
        """Load project templates"""
        try:
            for template_file in self.templates_path.glob("*.yaml"):
                with open(template_file, 'r') as f:
                    template_data = yaml.safe_load(f)
                    template = ProjectTemplate(**template_data)
                    self.templates[template.template_id] = template
        except Exception as e:
            logger.error(f"Failed to load templates: {e}")
    
    async def validate_project_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate project configuration"""
        try:
            # Validate using Pydantic model
            validated_config = ProjectConfiguration(**config)
            
            # Additional custom validations
            validation_results = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "suggestions": []
            }
            
            # Check if template exists
            if validated_config.template_id:
                if validated_config.template_id not in self.templates:
                    validation_results["errors"].append(f"Template '{validated_config.template_id}' not found")
                    validation_results["valid"] = False
                else:
                    template = self.templates[validated_config.template_id]
                    # Validate technology stack compatibility
                    if not set(validated_config.technology_stack).intersection(set(template.technology_stack)):
                        validation_results["warnings"].append(
                            f"Technology stack doesn't match template recommendations: {template.technology_stack}"
                        )
            
            # Check for project name conflicts
            existing_names = [p.name for p in self.active_projects.values()]
            if validated_config.project_name in existing_names:
                validation_results["errors"].append(f"Project name '{validated_config.project_name}' already exists")
                validation_results["valid"] = False
            
            # Suggest improvements
            if not validated_config.estimated_duration:
                validation_results["suggestions"].append("Consider adding estimated duration for better planning")
            
            if not validated_config.tags:
                validation_results["suggestions"].append("Adding tags helps with project organization and discovery")
            
            return {
                "validation_results": validation_results,
                "validated_config": validated_config.dict()
            }
            
        except Exception as e:
            return {
                "validation_results": {
                    "valid": False,
                    "errors": [f"Configuration validation failed: {str(e)}"],
                    "warnings": [],
                    "suggestions": []
                },
                "validated_config": None
            }
    
    async def create_project_from_template(self, project_config: Dict[str, Any], 
                                         template_id: str) -> Dict[str, Any]:
        """Create a new project from a template"""
        try:
            if template_id not in self.templates:
                raise ValueError(f"Template '{template_id}' not found")
            
            template = self.templates[template_id]
            project_id = f"proj_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hashlib.md5(project_config['project_name'].encode()).hexdigest()[:8]}"
            
            # Create project directory
            project_path = self.base_path / project_id
            project_path.mkdir(parents=True, exist_ok=True)
            
            # Create file structure from template
            await self._create_file_structure(project_path, template.file_structure, project_config)
            
            # Calculate project checksum
            checksum = await self._calculate_project_checksum(project_path)
            
            # Create project metadata
            metadata = ProjectMetadata(
                project_id=project_id,
                name=project_config['project_name'],
                description=project_config.get('description', ''),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat(),
                version="1.0.0",
                status="active",
                template_id=template_id,
                tags=project_config.get('tags', []),
                owner=project_config.get('owner', 'system'),
                collaborators=project_config.get('collaborators', []),
                workflow_type=template.workflow_type,
                technology_stack=template.technology_stack,
                estimated_duration=template.estimated_duration,
                actual_duration=None,
                completion_percentage=0.0,
                file_count=len(list(project_path.rglob("*"))),
                total_size_bytes=sum(f.stat().st_size for f in project_path.rglob("*") if f.is_file()),
                last_activity=datetime.now().isoformat(),
                checksum=checksum,
                custom_fields=project_config.get('custom_configuration', {})
            )
            
            # Save metadata
            await self._save_project_metadata(metadata)
            self.active_projects[project_id] = metadata
            
            # Execute post-creation steps
            post_creation_results = await self._execute_post_creation_steps(
                project_path, template.post_creation_steps, project_config
            )
            
            return {
                "success": True,
                "project_id": project_id,
                "project_path": str(project_path),
                "metadata": asdict(metadata),
                "post_creation_results": post_creation_results
            }
            
        except Exception as e:
            logger.error(f"Failed to create project from template: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def archive_project(self, project_id: str, archive_reason: str = "Manual archive") -> Dict[str, Any]:
        """Archive a project"""
        try:
            if project_id not in self.active_projects:
                raise ValueError(f"Project '{project_id}' not found")
            
            metadata = self.active_projects[project_id]
            project_path = self.base_path / project_id
            
            if not project_path.exists():
                raise ValueError(f"Project directory not found: {project_path}")
            
            # Create archive
            archive_name = f"{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            archive_file_path = self.archive_path / archive_name
            
            with zipfile.ZipFile(archive_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in project_path.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(project_path)
                        zipf.write(file_path, arcname)
            
            # Update metadata
            metadata.status = "archived"
            metadata.updated_at = datetime.now().isoformat()
            metadata.custom_fields["archive_reason"] = archive_reason
            metadata.custom_fields["archive_path"] = str(archive_file_path)
            metadata.custom_fields["archive_size"] = archive_file_path.stat().st_size
            
            # Save updated metadata
            await self._save_project_metadata(metadata)
            
            # Remove project directory
            shutil.rmtree(project_path)
            
            logger.info(f"Project {project_id} archived successfully")
            
            return {
                "success": True,
                "archive_path": str(archive_file_path),
                "archive_size": archive_file_path.stat().st_size,
                "metadata": asdict(metadata)
            }
            
        except Exception as e:
            logger.error(f"Failed to archive project {project_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def resume_project(self, project_id: str) -> Dict[str, Any]:
        """Resume an archived project"""
        try:
            if project_id not in self.active_projects:
                raise ValueError(f"Project '{project_id}' not found")
            
            metadata = self.active_projects[project_id]
            
            if metadata.status != "archived":
                raise ValueError(f"Project '{project_id}' is not archived (status: {metadata.status})")
            
            archive_path = metadata.custom_fields.get("archive_path")
            if not archive_path or not Path(archive_path).exists():
                raise ValueError(f"Archive file not found: {archive_path}")
            
            # Extract archive
            project_path = self.base_path / project_id
            project_path.mkdir(parents=True, exist_ok=True)
            
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                zipf.extractall(project_path)
            
            # Update metadata
            metadata.status = "active"
            metadata.updated_at = datetime.now().isoformat()
            metadata.last_activity = datetime.now().isoformat()
            
            # Recalculate project statistics
            metadata.file_count = len(list(project_path.rglob("*")))
            metadata.total_size_bytes = sum(f.stat().st_size for f in project_path.rglob("*") if f.is_file())
            metadata.checksum = await self._calculate_project_checksum(project_path)
            
            # Save updated metadata
            await self._save_project_metadata(metadata)
            
            logger.info(f"Project {project_id} resumed successfully")
            
            return {
                "success": True,
                "project_path": str(project_path),
                "metadata": asdict(metadata)
            }
            
        except Exception as e:
            logger.error(f"Failed to resume project {project_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_project_metadata(self, project_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update project metadata"""
        try:
            if project_id not in self.active_projects:
                raise ValueError(f"Project '{project_id}' not found")
            
            metadata = self.active_projects[project_id]
            
            # Update allowed fields
            allowed_updates = {
                'description', 'tags', 'collaborators', 'completion_percentage',
                'actual_duration', 'status', 'custom_fields'
            }
            
            for key, value in updates.items():
                if key in allowed_updates:
                    setattr(metadata, key, value)
            
            metadata.updated_at = datetime.now().isoformat()
            
            # Save updated metadata
            await self._save_project_metadata(metadata)
            
            return {
                "success": True,
                "metadata": asdict(metadata)
            }
            
        except Exception as e:
            logger.error(f"Failed to update project metadata: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_project_statistics(self) -> Dict[str, Any]:
        """Get comprehensive project statistics"""
        try:
            total_projects = len(self.active_projects)
            status_counts = {}
            technology_counts = {}
            template_usage = {}
            
            total_size = 0
            total_files = 0
            
            for metadata in self.active_projects.values():
                # Status distribution
                status_counts[metadata.status] = status_counts.get(metadata.status, 0) + 1
                
                # Technology stack analysis
                for tech in metadata.technology_stack:
                    technology_counts[tech] = technology_counts.get(tech, 0) + 1
                
                # Template usage
                if metadata.template_id:
                    template_usage[metadata.template_id] = template_usage.get(metadata.template_id, 0) + 1
                
                # Size and file counts
                total_size += metadata.total_size_bytes
                total_files += metadata.file_count
            
            return {
                "total_projects": total_projects,
                "status_distribution": status_counts,
                "technology_distribution": technology_counts,
                "template_usage": template_usage,
                "total_size_bytes": total_size,
                "total_files": total_files,
                "available_templates": len(self.templates),
                "average_project_size": total_size / total_projects if total_projects > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get project statistics: {e}")
            return {"error": str(e)}
    
    async def _create_file_structure(self, project_path: Path, file_structure: Dict[str, Any], 
                                   config: Dict[str, Any]):
        """Create file structure from template"""
        for item_name, item_config in file_structure.items():
            item_path = project_path / item_name
            
            if isinstance(item_config, dict):
                if item_config.get("type") == "directory":
                    item_path.mkdir(parents=True, exist_ok=True)
                    if "children" in item_config:
                        await self._create_file_structure(item_path, item_config["children"], config)
                elif item_config.get("type") == "file":
                    content = item_config.get("content", "")
                    # Replace template variables
                    content = content.replace("{{project_name}}", config.get("project_name", ""))
                    content = content.replace("{{description}}", config.get("description", ""))
                    
                    with open(item_path, 'w') as f:
                        f.write(content)
    
    async def _execute_post_creation_steps(self, project_path: Path, steps: List[str], 
                                         config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute post-creation steps"""
        results = []
        
        for step in steps:
            try:
                if step.startswith("npm install"):
                    # Handle npm installation
                    import subprocess
                    result = subprocess.run(
                        ["npm", "install"], 
                        cwd=project_path, 
                        capture_output=True, 
                        text=True
                    )
                    results.append({
                        "step": step,
                        "success": result.returncode == 0,
                        "output": result.stdout,
                        "error": result.stderr
                    })
                else:
                    # Generic step execution
                    results.append({
                        "step": step,
                        "success": True,
                        "output": f"Step '{step}' executed",
                        "error": None
                    })
            except Exception as e:
                results.append({
                    "step": step,
                    "success": False,
                    "output": None,
                    "error": str(e)
                })
        
        return results
    
    async def _calculate_project_checksum(self, project_path: Path) -> str:
        """Calculate project checksum for integrity verification"""
        hasher = hashlib.sha256()
        
        for file_path in sorted(project_path.rglob("*")):
            if file_path.is_file():
                with open(file_path, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hasher.update(chunk)
        
        return hasher.hexdigest()
    
    async def _save_project_metadata(self, metadata: ProjectMetadata):
        """Save project metadata to file"""
        metadata_file = self.metadata_path / f"{metadata.project_id}.json"
        with open(metadata_file, 'w') as f:
            json.dump(asdict(metadata), f, indent=2)

# Global project lifecycle manager
project_lifecycle_manager = ProjectLifecycleManager()
