#!/usr/bin/env python3
"""
Full BMAD Workflow Engine for Aetherforge

This module implements a comprehensive workflow engine that can parse and execute
BMAD (Behavior-driven Multi-Agent Development) workflow YAML definitions with
support for:

- Conditional steps and branching logic
- Optional tasks with graceful failure handling
- Dynamic agent assignment and load balancing
- Parallel and sequential execution
- Retry mechanisms and timeout handling
- Real-time monitoring and progress tracking
- Integration with pheromone communication system

Features:
- YAML workflow definition parsing with validation
- State machine-based execution engine
- Conditional logic (if/else, loops, switches)
- Dynamic agent selection based on capabilities
- Error handling and recovery mechanisms
- Comprehensive logging and monitoring
- Integration with Aetherforge orchestrator
"""

import os
import yaml
import json
import asyncio
import logging
import time
import uuid
import re
import copy
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum, IntEnum
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class WorkflowStatus(str, Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class StepStatus(str, Enum):
    """Individual step execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    OPTIONAL_FAILED = "optional_failed"
    RETRYING = "retrying"

class StepType(str, Enum):
    """Types of workflow steps"""
    TASK = "task"
    CONDITION = "condition"
    LOOP = "loop"
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    AGENT_ASSIGNMENT = "agent_assignment"
    PHEROMONE_DROP = "pheromone_drop"
    WAIT = "wait"
    CUSTOM = "custom"

class ConditionOperator(str, Enum):
    """Conditional operators for step conditions"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    LESS_THAN = "lt"
    GREATER_EQUAL = "ge"
    LESS_EQUAL = "le"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    REGEX_MATCH = "regex"
    EXISTS = "exists"
    NOT_EXISTS = "not_exists"
    IN_LIST = "in"
    NOT_IN_LIST = "not_in"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    IS_EMPTY = "is_empty"
    IS_NOT_EMPTY = "is_not_empty"
    AND = "and"
    OR = "or"
    NOT = "not"

class AgentSelectionStrategy(str, Enum):
    """Strategies for dynamic agent selection"""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    CAPABILITY_MATCH = "capability_match"
    RANDOM = "random"
    PRIORITY_BASED = "priority_based"
    CUSTOM = "custom"

# Legacy compatibility
PhaseStatus = StepStatus

@dataclass
class WorkflowVariable:
    """Workflow variable with type and value"""
    name: str
    value: Any
    type: str = "string"
    description: str = ""
    required: bool = False
    default: Any = None

@dataclass
class StepCondition:
    """Condition for conditional step execution"""
    variable: str
    operator: ConditionOperator
    value: Any
    description: str = ""

    # Complex condition support
    sub_conditions: List['StepCondition'] = field(default_factory=list)
    negate: bool = False

    def __post_init__(self):
        """Validate condition after initialization"""
        if self.operator in [ConditionOperator.AND, ConditionOperator.OR]:
            if not self.sub_conditions:
                raise ValueError(f"Operator {self.operator} requires sub_conditions")
        elif self.operator == ConditionOperator.NOT:
            if len(self.sub_conditions) != 1:
                raise ValueError("NOT operator requires exactly one sub_condition")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StepCondition':
        """Create condition from dictionary"""
        sub_conditions = []
        if 'sub_conditions' in data:
            sub_conditions = [cls.from_dict(sub) for sub in data['sub_conditions']]

        return cls(
            variable=data.get('variable', ''),
            operator=ConditionOperator(data.get('operator', 'eq')),
            value=data.get('value'),
            description=data.get('description', ''),
            sub_conditions=sub_conditions,
            negate=data.get('negate', False)
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert condition to dictionary"""
        result = {
            'variable': self.variable,
            'operator': self.operator.value,
            'value': self.value,
            'description': self.description,
            'negate': self.negate
        }

        if self.sub_conditions:
            result['sub_conditions'] = [sub.to_dict() for sub in self.sub_conditions]

        return result

@dataclass
class AgentRequirement:
    """Requirements for agent selection"""
    capabilities: List[str] = field(default_factory=list)
    min_performance: float = 0.0
    max_load: float = 1.0
    preferred_agents: List[str] = field(default_factory=list)
    excluded_agents: List[str] = field(default_factory=list)
    selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

@dataclass
class RetryConfig:
    """Retry configuration for steps"""
    max_attempts: int = 3
    delay_seconds: float = 1.0
    backoff_multiplier: float = 2.0
    max_delay_seconds: float = 60.0
    retry_on_errors: List[str] = field(default_factory=list)

@dataclass
class TimeoutConfig:
    """Timeout configuration for steps"""
    execution_timeout: float = 300.0  # 5 minutes default
    agent_response_timeout: float = 30.0
    total_timeout: float = 3600.0  # 1 hour default

@dataclass
class WorkflowStep:
    """Enhanced workflow step definition"""
    id: str
    name: str
    type: StepType
    description: str = ""

    # Execution configuration
    command: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    environment: Dict[str, str] = field(default_factory=dict)

    # Conditional execution
    condition: Optional[StepCondition] = None
    depends_on: List[str] = field(default_factory=list)

    # Agent assignment
    agent_requirements: Optional[AgentRequirement] = None
    assigned_agent: Optional[str] = None

    # Error handling
    optional: bool = False
    retry_config: Optional[RetryConfig] = None
    timeout_config: Optional[TimeoutConfig] = None
    on_failure: Optional[str] = None  # Step ID to execute on failure
    on_success: Optional[str] = None  # Step ID to execute on success

    # Parallel execution
    parallel_steps: List[str] = field(default_factory=list)
    wait_for_all: bool = True

    # Loop configuration
    loop_variable: Optional[str] = None
    loop_items: List[Any] = field(default_factory=list)
    loop_condition: Optional[StepCondition] = None
    max_iterations: int = 100

    # Output handling
    output_variables: Dict[str, str] = field(default_factory=dict)
    capture_output: bool = True

    # Metadata
    tags: Set[str] = field(default_factory=set)
    priority: int = 0
    estimated_duration: float = 60.0

@dataclass
class WorkflowPhase:
    """Legacy workflow phase - converted to WorkflowStep internally"""
    id: str
    name: str
    description: str
    agent: str
    creates: List[str] = field(default_factory=list)
    requires: List[str] = field(default_factory=list)
    optional_steps: List[str] = field(default_factory=list)
    duration_minutes: int = 30
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    outputs: List[str] = field(default_factory=list)
    notes: str = field(default="")
    condition: Optional[str] = None

    def to_workflow_step(self) -> WorkflowStep:
        """Convert legacy phase to modern workflow step"""
        return WorkflowStep(
            id=self.id,
            name=self.name,
            type=StepType.TASK,
            description=self.description,
            parameters={
                "creates": self.creates,
                "requires": self.requires,
                "optional_steps": self.optional_steps,
                "notes": self.notes
            },
            agent_requirements=AgentRequirement(
                preferred_agents=[self.agent] if self.agent else []
            ),
            estimated_duration=self.duration_minutes * 60,  # Convert to seconds
            condition=StepCondition(
                variable="legacy_condition",
                operator=ConditionOperator.EXISTS,
                value=self.condition
            ) if self.condition else None
        )

@dataclass
class WorkflowExecution:
    """Enhanced runtime execution state of a workflow"""
    id: str
    workflow_id: str
    status: WorkflowStatus
    started_at: float
    completed_at: Optional[float] = None

    # Execution context
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    step_results: Dict[str, Any] = field(default_factory=dict)
    step_status: Dict[str, StepStatus] = field(default_factory=dict)
    step_attempts: Dict[str, int] = field(default_factory=dict)

    # Agent assignments
    agent_assignments: Dict[str, str] = field(default_factory=dict)  # step_id -> agent_id
    agent_loads: Dict[str, float] = field(default_factory=dict)

    # Progress tracking
    total_steps: int = 0
    completed_steps: int = 0
    failed_steps: int = 0
    skipped_steps: int = 0
    step_start_times: Dict[str, float] = field(default_factory=dict)
    step_end_times: Dict[str, float] = field(default_factory=dict)

    # Error information
    last_error: Optional[str] = None
    error_details: Dict[str, Any] = field(default_factory=dict)

    # Performance metrics
    execution_time: float = 0.0
    agent_response_times: Dict[str, float] = field(default_factory=dict)

    # Legacy compatibility
    project_id: Optional[str] = None
    current_phase_index: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    phase_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    agent_team: Optional[Dict[str, Any]] = None
    pheromone_bus: Optional[Dict[str, Any]] = None

@dataclass
class WorkflowDefinition:
    """Enhanced workflow definition parsed from YAML"""
    id: str
    name: str
    version: str
    description: str = ""

    # Workflow metadata
    author: str = ""
    created_at: str = ""
    tags: Set[str] = field(default_factory=set)

    # Execution configuration
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    steps: Dict[str, WorkflowStep] = field(default_factory=dict)
    step_order: List[str] = field(default_factory=list)

    # Global configuration
    global_timeout: float = 3600.0  # 1 hour
    global_retry_config: Optional[RetryConfig] = None
    parallel_execution: bool = False
    max_concurrent_steps: int = 5

    # Agent configuration
    required_capabilities: List[str] = field(default_factory=list)
    preferred_agents: List[str] = field(default_factory=list)
    agent_selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

    # Integration settings
    pheromone_integration: bool = True
    progress_reporting: bool = True
    detailed_logging: bool = True

    # Legacy compatibility
    type: str = "bmad"
    project_types: List[str] = field(default_factory=list)
    phases: List[WorkflowPhase] = field(default_factory=list)
    total_duration_minutes: int = 0
    flow_diagram: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

# Exception Classes
class WorkflowValidationError(Exception):
    """Raised when workflow definition validation fails"""
    def __init__(self, message: str, errors: List[str] = None):
        super().__init__(message)
        self.errors = errors or []

# Import the enhanced exceptions from orchestrator
try:
    from .orchestrator import WorkflowExecutionError as BaseWorkflowExecutionError
except ImportError:
    # Fallback if orchestrator is not available
    class BaseWorkflowExecutionError(Exception):
        """Fallback WorkflowExecutionError"""
        def __init__(self, message: str, workflow_id: str = None, step_id: str = None, details: dict = None):
            super().__init__(message)
            self.workflow_id = workflow_id
            self.step_id = step_id
            self.details = details or {}
            self.error_type = "workflow_execution"

class WorkflowExecutionError(BaseWorkflowExecutionError):
    """Raised when workflow execution fails"""
    pass

class WorkflowTimeoutError(WorkflowExecutionError):
    """Raised when workflow execution times out"""

    def __init__(self, message: str, workflow_id: str = None, timeout_duration: float = None, details: dict = None):
        super().__init__(message, workflow_id=workflow_id, step_id=None, details=details)
        self.timeout_duration = timeout_duration
        self.error_type = "workflow_timeout"

    def __str__(self):
        base_msg = super().__str__()
        if self.timeout_duration:
            return f"{base_msg} (Timeout: {self.timeout_duration}s)"
        return base_msg

class AgentAssignmentError(WorkflowExecutionError):
    """Raised when agent assignment fails"""

    def __init__(self, message: str, workflow_id: str = None, step_id: str = None,
                 agent_requirements: dict = None, available_agents: list = None, details: dict = None):
        super().__init__(message, workflow_id=workflow_id, step_id=step_id, details=details)
        self.agent_requirements = agent_requirements or {}
        self.available_agents = available_agents or []
        self.error_type = "agent_assignment"

    def __str__(self):
        base_msg = super().__str__()
        context = []
        if self.agent_requirements:
            context.append(f"Required: {self.agent_requirements}")
        if self.available_agents:
            context.append(f"Available: {len(self.available_agents)} agents")

        if context:
            return f"{base_msg} [{', '.join(context)}]"
        return base_msg

class WorkflowYAMLParser:
    """Enhanced YAML parser for BMAD workflow definitions"""

    def __init__(self):
        self.validation_errors = []

    def parse_workflow_file(self, file_path: Union[str, Path]) -> WorkflowDefinition:
        """Parse workflow definition from YAML file"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise WorkflowValidationError(f"Workflow file not found: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_content = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise WorkflowValidationError(f"Invalid YAML syntax: {e}")
        except Exception as e:
            raise WorkflowValidationError(f"Error reading workflow file: {e}")

        return self.parse_workflow_dict(yaml_content)

    def parse_workflow_dict(self, yaml_data: Dict[str, Any]) -> WorkflowDefinition:
        """Parse workflow definition from dictionary"""
        self.validation_errors = []

        try:
            # Extract basic metadata
            workflow_id = yaml_data.get('id', str(uuid.uuid4()))
            name = yaml_data.get('name', 'Unnamed Workflow')
            version = yaml_data.get('version', '1.0.0')
            description = yaml_data.get('description', '')

            # Parse metadata
            author = yaml_data.get('author', '')
            created_at = yaml_data.get('created_at', datetime.now().isoformat())
            tags = set(yaml_data.get('tags', []))

            # Parse variables
            variables = self._parse_variables(yaml_data.get('variables', {}))

            # Parse steps
            steps, step_order = self._parse_steps(yaml_data.get('steps', {}))

            # Parse global configuration
            global_config = yaml_data.get('config', {})
            global_timeout = global_config.get('timeout', 3600.0)
            global_retry_config = self._parse_retry_config(global_config.get('retry'))
            parallel_execution = global_config.get('parallel_execution', False)
            max_concurrent_steps = global_config.get('max_concurrent_steps', 5)

            # Parse agent configuration
            agent_config = yaml_data.get('agents', {})
            required_capabilities = agent_config.get('required_capabilities', [])
            preferred_agents = agent_config.get('preferred_agents', [])
            agent_selection_strategy = AgentSelectionStrategy(
                agent_config.get('selection_strategy', 'capability_match')
            )

            # Parse integration settings
            integration_config = yaml_data.get('integration', {})
            pheromone_integration = integration_config.get('pheromone_integration', True)
            progress_reporting = integration_config.get('progress_reporting', True)
            detailed_logging = integration_config.get('detailed_logging', True)

            # Create workflow definition
            workflow = WorkflowDefinition(
                id=workflow_id,
                name=name,
                version=version,
                description=description,
                author=author,
                created_at=created_at,
                tags=tags,
                variables=variables,
                steps=steps,
                step_order=step_order,
                global_timeout=global_timeout,
                global_retry_config=global_retry_config,
                parallel_execution=parallel_execution,
                max_concurrent_steps=max_concurrent_steps,
                required_capabilities=required_capabilities,
                preferred_agents=preferred_agents,
                agent_selection_strategy=agent_selection_strategy,
                pheromone_integration=pheromone_integration,
                progress_reporting=progress_reporting,
                detailed_logging=detailed_logging
            )

            # Handle legacy format conversion
            if 'phases' in yaml_data:
                workflow = self._convert_legacy_workflow(yaml_data, workflow)

            # Validate workflow
            self._validate_workflow(workflow)

            if self.validation_errors:
                raise WorkflowValidationError(
                    "Workflow validation failed",
                    errors=self.validation_errors
                )

            return workflow

        except WorkflowValidationError:
            raise
        except Exception as e:
            raise WorkflowValidationError(f"Error parsing workflow: {e}")

    def _parse_variables(self, variables_data: Dict[str, Any]) -> Dict[str, WorkflowVariable]:
        """Parse workflow variables"""
        variables = {}

        for name, var_data in variables_data.items():
            if isinstance(var_data, dict):
                variable = WorkflowVariable(
                    name=name,
                    value=var_data.get('value'),
                    type=var_data.get('type', 'string'),
                    description=var_data.get('description', ''),
                    required=var_data.get('required', False),
                    default=var_data.get('default')
                )
            else:
                # Simple value format
                variable = WorkflowVariable(
                    name=name,
                    value=var_data,
                    type='string'
                )

            variables[name] = variable

        return variables

    def _parse_steps(self, steps_data: Dict[str, Any]) -> Tuple[Dict[str, WorkflowStep], List[str]]:
        """Parse workflow steps and determine execution order"""
        steps = {}
        step_order = []

        for step_id, step_data in steps_data.items():
            step = self._parse_single_step(step_id, step_data)
            steps[step_id] = step
            step_order.append(step_id)

        # Resolve dependencies and optimize order
        step_order = self._resolve_step_dependencies(steps, step_order)

        return steps, step_order

    def _parse_single_step(self, step_id: str, step_data: Dict[str, Any]) -> WorkflowStep:
        """Parse a single workflow step"""
        step = WorkflowStep(
            id=step_id,
            name=step_data.get('name', step_id),
            type=StepType(step_data.get('type', 'task')),
            description=step_data.get('description', ''),
            command=step_data.get('command'),
            parameters=step_data.get('parameters', {}),
            environment=step_data.get('environment', {}),
            depends_on=step_data.get('depends_on', []),
            optional=step_data.get('optional', False),
            parallel_steps=step_data.get('parallel_steps', []),
            wait_for_all=step_data.get('wait_for_all', True),
            output_variables=step_data.get('output_variables', {}),
            capture_output=step_data.get('capture_output', True),
            tags=set(step_data.get('tags', [])),
            priority=step_data.get('priority', 0),
            estimated_duration=step_data.get('estimated_duration', 60.0)
        )

        # Parse condition
        if 'condition' in step_data:
            step.condition = StepCondition.from_dict(step_data['condition'])

        # Parse agent requirements
        if 'agent' in step_data:
            agent_data = step_data['agent']
            if isinstance(agent_data, str):
                # Simple agent name
                step.agent_requirements = AgentRequirement(
                    preferred_agents=[agent_data]
                )
            else:
                # Complex agent requirements
                step.agent_requirements = AgentRequirement(
                    capabilities=agent_data.get('capabilities', []),
                    min_performance=agent_data.get('min_performance', 0.0),
                    max_load=agent_data.get('max_load', 1.0),
                    preferred_agents=agent_data.get('preferred_agents', []),
                    excluded_agents=agent_data.get('excluded_agents', []),
                    selection_strategy=AgentSelectionStrategy(
                        agent_data.get('selection_strategy', 'capability_match')
                    )
                )

        # Parse retry configuration
        if 'retry' in step_data:
            step.retry_config = self._parse_retry_config(step_data['retry'])

        # Parse timeout configuration
        if 'timeout' in step_data:
            timeout_data = step_data['timeout']
            step.timeout_config = TimeoutConfig(
                execution_timeout=timeout_data.get('execution', 300.0),
                agent_response_timeout=timeout_data.get('agent_response', 30.0),
                total_timeout=timeout_data.get('total', 3600.0)
            )

        # Parse failure/success handlers
        step.on_failure = step_data.get('on_failure')
        step.on_success = step_data.get('on_success')

        # Parse loop configuration
        if step.type == StepType.LOOP:
            step.loop_variable = step_data.get('loop_variable')
            step.loop_items = step_data.get('loop_items', [])
            if 'loop_condition' in step_data:
                step.loop_condition = StepCondition.from_dict(step_data['loop_condition'])
            step.max_iterations = step_data.get('max_iterations', 100)

        return step

    def _parse_retry_config(self, retry_data: Optional[Dict[str, Any]]) -> Optional[RetryConfig]:
        """Parse retry configuration"""
        if not retry_data:
            return None

    def _resolve_step_dependencies(self, steps: Dict[str, WorkflowStep], initial_order: List[str]) -> List[str]:
        """Resolve step dependencies and create optimal execution order"""
        # Topological sort for dependency resolution
        in_degree = {step_id: 0 for step_id in steps}

        # Calculate in-degrees
        for step in steps.values():
            for dep in step.depends_on:
                if dep in in_degree:
                    in_degree[step.id] += 1

        # Find steps with no dependencies
        queue = deque([step_id for step_id, degree in in_degree.items() if degree == 0])
        result = []

        while queue:
            step_id = queue.popleft()
            result.append(step_id)

            # Update dependencies
            step = steps[step_id]
            for other_step in steps.values():
                if step_id in other_step.depends_on:
                    in_degree[other_step.id] -= 1
                    if in_degree[other_step.id] == 0:
                        queue.append(other_step.id)

        # Check for circular dependencies
        if len(result) != len(steps):
            remaining = set(steps.keys()) - set(result)
            logger.warning(f"Circular dependencies detected in steps: {remaining}")
            # Add remaining steps to the end
            result.extend(remaining)

        return result

    def _convert_legacy_workflow(self, yaml_data: Dict[str, Any], workflow: WorkflowDefinition) -> WorkflowDefinition:
        """Convert legacy workflow format to modern format"""
        if 'phases' in yaml_data:
            phases_data = yaml_data['phases']

            for i, phase_data in enumerate(phases_data):
                phase_id = phase_data.get('id', f"phase_{i}")

                # Convert phase to step
                step = WorkflowStep(
                    id=phase_id,
                    name=phase_data.get('name', f"Phase {i+1}"),
                    type=StepType.TASK,
                    description=phase_data.get('description', ''),
                    parameters={
                        'creates': phase_data.get('creates', []),
                        'requires': phase_data.get('requires', []),
                        'optional_steps': phase_data.get('optional_steps', [])
                    },
                    agent_requirements=AgentRequirement(
                        preferred_agents=[phase_data.get('agent', '')] if phase_data.get('agent') else []
                    ),
                    estimated_duration=phase_data.get('duration_minutes', 30) * 60,
                    depends_on=[f"phase_{i-1}"] if i > 0 else []
                )

                workflow.steps[phase_id] = step
                if phase_id not in workflow.step_order:
                    workflow.step_order.append(phase_id)

        return workflow

    def _validate_workflow(self, workflow: WorkflowDefinition) -> None:
        """Validate workflow definition"""
        # Check required fields
        if not workflow.name:
            self.validation_errors.append("Workflow name is required")

        if not workflow.steps and not workflow.phases:
            self.validation_errors.append("Workflow must have at least one step or phase")

        # Validate step dependencies
        for step_id, step in workflow.steps.items():
            for dep in step.depends_on:
                if dep not in workflow.steps:
                    self.validation_errors.append(
                        f"Step '{step_id}' depends on non-existent step '{dep}'"
                    )

        # Check for circular dependencies
        self._check_circular_dependencies(workflow.steps)

        # Validate conditions reference existing variables or are evaluable
        for step_id, step in workflow.steps.items():
            if step.condition:
                self._validate_condition(step.condition, workflow.variables, step_id)

    def _validate_condition(self, condition: StepCondition, variables: Dict[str, WorkflowVariable], step_id: str) -> None:
        """Validate a step condition"""
        if condition.operator in [ConditionOperator.AND, ConditionOperator.OR, ConditionOperator.NOT]:
            # Validate sub-conditions
            for sub_condition in condition.sub_conditions:
                self._validate_condition(sub_condition, variables, step_id)
        else:
            # Validate variable reference
            if condition.variable and condition.variable not in variables:
                # Check if it's a special variable (like step results)
                if not condition.variable.startswith('step.') and not condition.variable.startswith('env.'):
                    self.validation_errors.append(
                        f"Step '{step_id}' condition references undefined variable '{condition.variable}'"
                    )

    def _check_circular_dependencies(self, steps: Dict[str, WorkflowStep]) -> None:
        """Check for circular dependencies in workflow steps"""
        def has_cycle(step_id: str, visited: Set[str], rec_stack: Set[str]) -> bool:
            visited.add(step_id)
            rec_stack.add(step_id)

            step = steps.get(step_id)
            if step:
                for dep in step.depends_on:
                    if dep not in visited:
                        if has_cycle(dep, visited, rec_stack):
                            return True
                    elif dep in rec_stack:
                        return True

            rec_stack.remove(step_id)
            return False

        visited = set()
        for step_id in steps:
            if step_id not in visited:
                if has_cycle(step_id, visited, set()):
                    self.validation_errors.append(
                        f"Circular dependency detected involving step '{step_id}'"
                    )

    def _parse_variables(self, variables_data: Dict[str, Any]) -> Dict[str, WorkflowVariable]:
        """Parse workflow variables"""
        variables = {}

        for name, var_data in variables_data.items():
            if isinstance(var_data, dict):
                variable = WorkflowVariable(
                    name=name,
                    value=var_data.get('value'),
                    type=var_data.get('type', 'string'),
                    description=var_data.get('description', ''),
                    required=var_data.get('required', False),
                    default=var_data.get('default')
                )
            else:
                # Simple value
                variable = WorkflowVariable(
                    name=name,
                    value=var_data,
                    type=type(var_data).__name__
                )

            variables[name] = variable

        return variables

    def _parse_steps(self, steps_data: Dict[str, Any]) -> Tuple[Dict[str, WorkflowStep], List[str]]:
        """Parse workflow steps"""
        steps = {}
        step_order = []

        for step_id, step_data in steps_data.items():
            try:
                step = self._parse_single_step(step_id, step_data)
                steps[step_id] = step
                step_order.append(step_id)
            except Exception as e:
                self.validation_errors.append(f"Error parsing step '{step_id}': {e}")

        return steps, step_order

    def _parse_single_step(self, step_id: str, step_data: Dict[str, Any]) -> WorkflowStep:
        """Parse a single workflow step"""
        name = step_data.get('name', step_id)
        step_type = StepType(step_data.get('type', 'task'))
        description = step_data.get('description', '')

        # Parse execution configuration
        command = step_data.get('command')
        parameters = step_data.get('parameters', {})
        environment = step_data.get('environment', {})

        # Parse conditional execution
        condition = self._parse_condition(step_data.get('condition'))
        depends_on = step_data.get('depends_on', [])
        if isinstance(depends_on, str):
            depends_on = [depends_on]

        # Parse agent requirements
        agent_requirements = self._parse_agent_requirements(step_data.get('agent'))
        assigned_agent = step_data.get('assigned_agent')

        # Parse error handling
        optional = step_data.get('optional', False)
        retry_config = self._parse_retry_config(step_data.get('retry'))
        timeout_config = self._parse_timeout_config(step_data.get('timeout'))
        on_failure = step_data.get('on_failure')
        on_success = step_data.get('on_success')

        # Parse parallel execution
        parallel_steps = step_data.get('parallel_steps', [])
        wait_for_all = step_data.get('wait_for_all', True)

        # Parse loop configuration
        loop_variable = step_data.get('loop_variable')
        loop_items = step_data.get('loop_items', [])
        loop_condition = self._parse_condition(step_data.get('loop_condition'))
        max_iterations = step_data.get('max_iterations', 100)

        # Parse output handling
        output_variables = step_data.get('output_variables', {})
        capture_output = step_data.get('capture_output', True)

        # Parse metadata
        tags = set(step_data.get('tags', []))
        priority = step_data.get('priority', 0)
        estimated_duration = step_data.get('estimated_duration', 60.0)

        return WorkflowStep(
            id=step_id,
            name=name,
            type=step_type,
            description=description,
            command=command,
            parameters=parameters,
            environment=environment,
            condition=condition,
            depends_on=depends_on,
            agent_requirements=agent_requirements,
            assigned_agent=assigned_agent,
            optional=optional,
            retry_config=retry_config,
            timeout_config=timeout_config,
            on_failure=on_failure,
            on_success=on_success,
            parallel_steps=parallel_steps,
            wait_for_all=wait_for_all,
            loop_variable=loop_variable,
            loop_items=loop_items,
            loop_condition=loop_condition,
            max_iterations=max_iterations,
            output_variables=output_variables,
            capture_output=capture_output,
            tags=tags,
            priority=priority,
            estimated_duration=estimated_duration
        )

    def _parse_condition(self, condition_data: Any) -> Optional[StepCondition]:
        """Parse step condition with support for complex conditions"""
        if not condition_data:
            return None

        if isinstance(condition_data, str):
            # Simple existence check
            return StepCondition(
                variable=condition_data,
                operator=ConditionOperator.EXISTS,
                value=True
            )

        if isinstance(condition_data, dict):
            # Handle complex conditions
            if 'conditions' in condition_data or 'sub_conditions' in condition_data:
                return self._parse_complex_condition(condition_data)

            # Simple condition
            variable = condition_data.get('variable', '')
            operator_str = condition_data.get('operator', 'eq')

            # Validate operator
            try:
                operator = ConditionOperator(operator_str)
            except ValueError:
                logger.warning(f"Unknown condition operator: {operator_str}, defaulting to 'eq'")
                operator = ConditionOperator.EQUALS

            value = condition_data.get('value')
            description = condition_data.get('description', '')
            negate = condition_data.get('negate', False)

            return StepCondition(
                variable=variable,
                operator=operator,
                value=value,
                description=description,
                negate=negate
            )

        if isinstance(condition_data, list):
            # List of conditions - treat as AND by default
            sub_conditions = []
            for item in condition_data:
                parsed = self._parse_condition(item)
                if parsed:
                    sub_conditions.append(parsed)

            if sub_conditions:
                return StepCondition(
                    variable="",
                    operator=ConditionOperator.AND,
                    value=None,
                    sub_conditions=sub_conditions
                )

        return None

    def _parse_complex_condition(self, condition_data: Dict[str, Any]) -> Optional[StepCondition]:
        """Parse complex condition with logical operators"""
        operator_str = condition_data.get('operator', 'and')

        try:
            operator = ConditionOperator(operator_str)
        except ValueError:
            logger.warning(f"Unknown logical operator: {operator_str}, defaulting to 'and'")
            operator = ConditionOperator.AND

        # Parse sub-conditions
        sub_conditions = []
        conditions_data = condition_data.get('conditions', condition_data.get('sub_conditions', []))

        for sub_data in conditions_data:
            parsed = self._parse_condition(sub_data)
            if parsed:
                sub_conditions.append(parsed)

        if not sub_conditions:
            logger.warning("Complex condition has no valid sub-conditions")
            return None

        description = condition_data.get('description', '')
        negate = condition_data.get('negate', False)

        return StepCondition(
            variable="",
            operator=operator,
            value=None,
            description=description,
            sub_conditions=sub_conditions,
            negate=negate
        )

    def _parse_agent_requirements(self, agent_data: Any) -> Optional[AgentRequirement]:
        """Parse agent requirements"""
        if not agent_data:
            return None

        if isinstance(agent_data, str):
            # Simple agent preference
            return AgentRequirement(
                preferred_agents=[agent_data]
            )

        if isinstance(agent_data, dict):
            capabilities = agent_data.get('capabilities', [])
            min_performance = agent_data.get('min_performance', 0.0)
            max_load = agent_data.get('max_load', 1.0)
            preferred_agents = agent_data.get('preferred_agents', [])
            excluded_agents = agent_data.get('excluded_agents', [])
            selection_strategy = AgentSelectionStrategy(
                agent_data.get('selection_strategy', 'capability_match')
            )

            return AgentRequirement(
                capabilities=capabilities,
                min_performance=min_performance,
                max_load=max_load,
                preferred_agents=preferred_agents,
                excluded_agents=excluded_agents,
                selection_strategy=selection_strategy
            )

        return None

    def _parse_retry_config(self, retry_data: Any) -> Optional[RetryConfig]:
        """Parse retry configuration"""
        if not retry_data:
            return None

        if isinstance(retry_data, int):
            # Simple max attempts
            return RetryConfig(max_attempts=retry_data)

        if isinstance(retry_data, dict):
            return RetryConfig(
                max_attempts=retry_data.get('max_attempts', 3),
                delay_seconds=retry_data.get('delay_seconds', 1.0),
                backoff_multiplier=retry_data.get('backoff_multiplier', 2.0),
                max_delay_seconds=retry_data.get('max_delay_seconds', 60.0),
                retry_on_errors=retry_data.get('retry_on_errors', [])
            )

        return None

    def _parse_timeout_config(self, timeout_data: Any) -> Optional[TimeoutConfig]:
        """Parse timeout configuration"""
        if not timeout_data:
            return None

        if isinstance(timeout_data, (int, float)):
            # Simple execution timeout
            return TimeoutConfig(execution_timeout=float(timeout_data))

        if isinstance(timeout_data, dict):
            return TimeoutConfig(
                execution_timeout=timeout_data.get('execution_timeout', 300.0),
                agent_response_timeout=timeout_data.get('agent_response_timeout', 30.0),
                total_timeout=timeout_data.get('total_timeout', 3600.0)
            )

        return None

    def _parse_legacy_phases(self, phases_data: List[Dict[str, Any]]) -> List[WorkflowPhase]:
        """Parse legacy workflow phases for backward compatibility"""
        phases = []

        for phase_data in phases_data:
            try:
                phase = WorkflowPhase(
                    id=phase_data.get('id', str(uuid.uuid4())),
                    name=phase_data.get('name', 'Unnamed Phase'),
                    description=phase_data.get('description', ''),
                    agent=phase_data.get('agent', ''),
                    creates=phase_data.get('creates', []),
                    requires=phase_data.get('requires', []),
                    optional_steps=phase_data.get('optional_steps', []),
                    duration_minutes=phase_data.get('duration_minutes', 30),
                    notes=phase_data.get('notes', ''),
                    condition=phase_data.get('condition')
                )
                phases.append(phase)
            except Exception as e:
                self.validation_errors.append(f"Error parsing legacy phase: {e}")

        return phases

    def _validate_workflow(self, workflow: WorkflowDefinition) -> None:
        """Validate workflow definition"""
        # Check required fields
        if not workflow.name:
            self.validation_errors.append("Workflow name is required")

        if not workflow.steps and not workflow.phases:
            self.validation_errors.append("Workflow must have at least one step or phase")

        # Validate step dependencies
        for step_id, step in workflow.steps.items():
            for dep in step.depends_on:
                if dep not in workflow.steps:
                    self.validation_errors.append(
                        f"Step '{step_id}' depends on non-existent step '{dep}'"
                    )

        # Check for circular dependencies
        self._check_circular_dependencies(workflow.steps)

        # Validate conditions reference existing variables
        for step_id, step in workflow.steps.items():
            if step.condition:
                if step.condition.variable not in workflow.variables:
                    self.validation_errors.append(
                        f"Step '{step_id}' condition references undefined variable '{step.condition.variable}'"
                    )

        # Validate agent requirements
        for step_id, step in workflow.steps.items():
            if step.agent_requirements:
                # Check if required capabilities are reasonable
                if len(step.agent_requirements.capabilities) > 10:
                    self.validation_errors.append(
                        f"Step '{step_id}' has too many required capabilities (max 10)"
                    )

    def _check_circular_dependencies(self, steps: Dict[str, WorkflowStep]) -> None:
        """Check for circular dependencies in workflow steps"""
        def has_cycle(step_id: str, visited: Set[str], rec_stack: Set[str]) -> bool:
            visited.add(step_id)
            rec_stack.add(step_id)

            step = steps.get(step_id)
            if step:
                for dep in step.depends_on:
                    if dep not in visited:
                        if has_cycle(dep, visited, rec_stack):
                            return True
                    elif dep in rec_stack:
                        return True

            rec_stack.remove(step_id)
            return False

        visited = set()
        for step_id in steps:
            if step_id not in visited:
                if has_cycle(step_id, visited, set()):
                    self.validation_errors.append(
                        f"Circular dependency detected involving step '{step_id}'"
                    )

class WorkflowTemplateManager:
    """Manages workflow templates and template selection"""

    def __init__(self):
        self.templates: Dict[str, WorkflowDefinition] = {}
        self.template_categories: Dict[str, List[str]] = {}
        self.custom_templates: Dict[str, WorkflowDefinition] = {}
        self._load_builtin_templates()

    def _load_builtin_templates(self):
        """Load built-in BMAD workflow templates"""
        # Load templates from BMAD-METHOD workflows
        builtin_templates = {
            'greenfield-fullstack': self._create_greenfield_fullstack_template(),
            'greenfield-service': self._create_greenfield_service_template(),
            'greenfield-ui': self._create_greenfield_ui_template(),
            'brownfield-fullstack': self._create_brownfield_fullstack_template(),
            'brownfield-service': self._create_brownfield_service_template(),
            'brownfield-ui': self._create_brownfield_ui_template(),
            'game-prototype': self._create_game_prototype_template(),
            'api-service': self._create_api_service_template(),
            'mobile-app': self._create_mobile_app_template(),
            'desktop-app': self._create_desktop_app_template()
        }

        self.templates.update(builtin_templates)

        # Categorize templates
        self.template_categories = {
            'greenfield': ['greenfield-fullstack', 'greenfield-service', 'greenfield-ui'],
            'brownfield': ['brownfield-fullstack', 'brownfield-service', 'brownfield-ui'],
            'specialized': ['game-prototype', 'api-service', 'mobile-app', 'desktop-app'],
            'web': ['greenfield-fullstack', 'greenfield-ui', 'brownfield-fullstack', 'brownfield-ui'],
            'backend': ['greenfield-service', 'brownfield-service', 'api-service'],
            'mobile': ['mobile-app'],
            'desktop': ['desktop-app'],
            'gaming': ['game-prototype']
        }

    def get_template(self, template_id: str) -> Optional[WorkflowDefinition]:
        """Get a workflow template by ID"""
        return self.templates.get(template_id) or self.custom_templates.get(template_id)

    def list_templates(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """List available templates, optionally filtered by category"""
        all_templates = {**self.templates, **self.custom_templates}

        if category and category in self.template_categories:
            template_ids = self.template_categories[category]
            filtered_templates = {tid: all_templates[tid] for tid in template_ids if tid in all_templates}
        else:
            filtered_templates = all_templates

        return [
            {
                'id': tid,
                'name': template.name,
                'description': template.description,
                'version': template.version,
                'author': template.author,
                'tags': list(template.tags),
                'project_types': getattr(template, 'project_types', []),
                'estimated_duration': sum(step.estimated_duration for step in template.steps.values()) / 60,  # in minutes
                'complexity': self._calculate_complexity(template)
            }
            for tid, template in filtered_templates.items()
        ]

    def search_templates(self, query: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search templates by query and project type"""
        all_templates = self.list_templates()

        results = []
        query_lower = query.lower()

        for template_info in all_templates:
            # Check if query matches name, description, or tags
            matches_query = (
                query_lower in template_info['name'].lower() or
                query_lower in template_info['description'].lower() or
                any(query_lower in tag.lower() for tag in template_info['tags'])
            )

            # Check project type if specified
            matches_project_type = (
                not project_type or
                project_type in template_info['project_types'] or
                any(project_type in pt for pt in template_info['project_types'])
            )

            if matches_query and matches_project_type:
                results.append(template_info)

        # Sort by relevance (exact matches first, then partial matches)
        results.sort(key=lambda t: (
            query_lower not in t['name'].lower(),  # Exact name matches first
            query_lower not in t['description'].lower(),  # Then description matches
            t['complexity']  # Then by complexity (simpler first)
        ))

        return results

    def add_custom_template(self, template_id: str, template: WorkflowDefinition):
        """Add a custom workflow template"""
        self.custom_templates[template_id] = template

    def remove_custom_template(self, template_id: str) -> bool:
        """Remove a custom workflow template"""
        if template_id in self.custom_templates:
            del self.custom_templates[template_id]
            return True
        return False

    def _calculate_complexity(self, template: WorkflowDefinition) -> int:
        """Calculate template complexity score"""
        complexity = 0
        complexity += len(template.steps)  # Number of steps
        complexity += sum(1 for step in template.steps.values() if step.condition)  # Conditional steps
        complexity += sum(len(step.depends_on) for step in template.steps.values())  # Dependencies
        complexity += sum(1 for step in template.steps.values() if step.parallel_steps)  # Parallel steps
        return complexity

    def _create_greenfield_fullstack_template(self) -> WorkflowDefinition:
        """Create Greenfield Fullstack workflow template"""
        steps = {
            "analyze_requirements": WorkflowStep(
                id="analyze_requirements",
                name="Analyze Requirements",
                type=StepType.TASK,
                description="Analyze project requirements and create detailed specifications",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "requirements"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"requirements_doc": "requirements.md", "user_stories": "user_stories.json"}
            ),
            "design_architecture": WorkflowStep(
                id="design_architecture",
                name="Design System Architecture",
                type=StepType.TASK,
                description="Design system architecture and technology stack",
                depends_on=["analyze_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "design"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"architecture_doc": "architecture.md", "tech_stack": "tech_stack.json"}
            ),
            "setup_project": WorkflowStep(
                id="setup_project",
                name="Setup Project Structure",
                type=StepType.TASK,
                description="Create project structure and initial configuration",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["project_setup", "scaffolding"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"project_structure": "project_structure.json"}
            ),
            "implement_backend": WorkflowStep(
                id="implement_backend",
                name="Implement Backend Services",
                type=StepType.PARALLEL,
                description="Implement backend API and services",
                depends_on=["setup_project"],
                parallel_steps=["create_api_endpoints", "setup_database", "implement_auth"],
                wait_for_all=True,
                estimated_duration=3600,  # 60 minutes
            ),
            "create_api_endpoints": WorkflowStep(
                id="create_api_endpoints",
                name="Create API Endpoints",
                type=StepType.TASK,
                description="Create REST API endpoints",
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "api"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"api_endpoints": "api_endpoints.json"}
            ),
            "setup_database": WorkflowStep(
                id="setup_database",
                name="Setup Database",
                type=StepType.TASK,
                description="Setup database schema and migrations",
                agent_requirements=AgentRequirement(
                    capabilities=["database", "backend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"database_schema": "schema.sql"}
            ),
            "implement_auth": WorkflowStep(
                id="implement_auth",
                name="Implement Authentication",
                type=StepType.TASK,
                description="Implement user authentication and authorization",
                agent_requirements=AgentRequirement(
                    capabilities=["security", "auth", "backend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2700,  # 45 minutes
                output_variables={"auth_system": "auth_system.json"}
            ),
            "implement_frontend": WorkflowStep(
                id="implement_frontend",
                name="Implement Frontend",
                type=StepType.TASK,
                description="Create frontend user interface",
                depends_on=["implement_backend"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=4200,  # 70 minutes
                output_variables={"frontend_components": "components.json"}
            ),
            "integrate_systems": WorkflowStep(
                id="integrate_systems",
                name="Integrate Frontend and Backend",
                type=StepType.TASK,
                description="Integrate frontend with backend services",
                depends_on=["implement_frontend"],
                agent_requirements=AgentRequirement(
                    capabilities=["integration", "fullstack"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"integration_config": "integration.json"}
            ),
            "run_tests": WorkflowStep(
                id="run_tests",
                name="Run Comprehensive Tests",
                type=StepType.TASK,
                description="Run unit, integration, and end-to-end tests",
                depends_on=["integrate_systems"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "qa"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"test_results": "test_results.json"},
                retry_config=RetryConfig(max_attempts=3, delay_seconds=30)
            ),
            "deploy_application": WorkflowStep(
                id="deploy_application",
                name="Deploy Application",
                type=StepType.TASK,
                description="Deploy application to production environment",
                depends_on=["run_tests"],
                condition=StepCondition(
                    variable="step.run_tests.success",
                    operator=ConditionOperator.EQUALS,
                    value=True
                ),
                agent_requirements=AgentRequirement(
                    capabilities=["deployment", "devops"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"deployment_url": "deployment.json"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete fullstack application development from requirements to deployment",
            author="Aetherforge",
            tags={"greenfield", "fullstack", "web", "complete"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=14400,  # 4 hours
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["analysis", "architecture", "frontend", "backend", "testing", "deployment"],
            project_types=["web_application", "fullstack", "saas"],
            total_duration_minutes=240  # 4 hours
        )

    def _create_greenfield_service_template(self) -> WorkflowDefinition:
        """Create Greenfield Service workflow template"""
        steps = {
            "analyze_service_requirements": WorkflowStep(
                id="analyze_service_requirements",
                name="Analyze Service Requirements",
                type=StepType.TASK,
                description="Analyze service requirements and API specifications",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "api_design"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"service_spec": "service_spec.json"}
            ),
            "design_service_architecture": WorkflowStep(
                id="design_service_architecture",
                name="Design Service Architecture",
                type=StepType.TASK,
                description="Design microservice architecture and data models",
                depends_on=["analyze_service_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "microservices"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"service_architecture": "architecture.json"}
            ),
            "setup_service_project": WorkflowStep(
                id="setup_service_project",
                name="Setup Service Project",
                type=StepType.TASK,
                description="Create service project structure and configuration",
                depends_on=["design_service_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["project_setup", "backend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=900,  # 15 minutes
                output_variables={"project_structure": "structure.json"}
            ),
            "implement_core_logic": WorkflowStep(
                id="implement_core_logic",
                name="Implement Core Business Logic",
                type=StepType.TASK,
                description="Implement core service business logic",
                depends_on=["setup_service_project"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "business_logic"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2700,  # 45 minutes
                output_variables={"core_modules": "modules.json"}
            ),
            "implement_api_layer": WorkflowStep(
                id="implement_api_layer",
                name="Implement API Layer",
                type=StepType.TASK,
                description="Implement REST/GraphQL API endpoints",
                depends_on=["implement_core_logic"],
                agent_requirements=AgentRequirement(
                    capabilities=["api", "backend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"api_endpoints": "endpoints.json"}
            ),
            "setup_data_persistence": WorkflowStep(
                id="setup_data_persistence",
                name="Setup Data Persistence",
                type=StepType.TASK,
                description="Setup database and data access layer",
                depends_on=["implement_api_layer"],
                agent_requirements=AgentRequirement(
                    capabilities=["database", "backend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"data_layer": "data_layer.json"}
            ),
            "implement_service_tests": WorkflowStep(
                id="implement_service_tests",
                name="Implement Service Tests",
                type=StepType.TASK,
                description="Create unit and integration tests for the service",
                depends_on=["setup_data_persistence"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "backend"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=2100,  # 35 minutes
                output_variables={"test_suite": "tests.json"}
            ),
            "setup_monitoring": WorkflowStep(
                id="setup_monitoring",
                name="Setup Monitoring and Logging",
                type=StepType.TASK,
                description="Setup service monitoring, logging, and health checks",
                depends_on=["implement_service_tests"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["monitoring", "devops"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"monitoring_config": "monitoring.json"}
            ),
            "containerize_service": WorkflowStep(
                id="containerize_service",
                name="Containerize Service",
                type=StepType.TASK,
                description="Create Docker container and deployment configuration",
                depends_on=["setup_monitoring"],
                agent_requirements=AgentRequirement(
                    capabilities=["docker", "deployment"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1500,  # 25 minutes
                output_variables={"container_config": "docker.json"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-service",
            name="Greenfield Service Development",
            version="1.0.0",
            description="Backend service development with API, database, and deployment",
            author="Aetherforge",
            tags={"greenfield", "backend", "service", "api"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=7200,  # 2 hours
            parallel_execution=False,
            required_capabilities=["analysis", "architecture", "backend", "api", "database", "testing"],
            project_types=["microservice", "api", "backend_service"],
            total_duration_minutes=120  # 2 hours
        )

    def _create_greenfield_ui_template(self) -> WorkflowDefinition:
        """Create Greenfield UI workflow template"""
        steps = {
            "analyze_ui_requirements": WorkflowStep(
                id="analyze_ui_requirements",
                name="Analyze UI Requirements",
                type=StepType.TASK,
                description="Analyze user interface requirements and user experience needs",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "ui_design"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"ui_requirements": "ui_requirements.json"}
            ),
            "design_ui_mockups": WorkflowStep(
                id="design_ui_mockups",
                name="Design UI Mockups",
                type=StepType.TASK,
                description="Create UI mockups and wireframes",
                depends_on=["analyze_ui_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["ui_design", "wireframing"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"mockups": "mockups.json", "wireframes": "wireframes.json"}
            ),
            "setup_frontend_project": WorkflowStep(
                id="setup_frontend_project",
                name="Setup Frontend Project",
                type=StepType.TASK,
                description="Create frontend project structure and configuration",
                depends_on=["design_ui_mockups"],
                agent_requirements=AgentRequirement(
                    capabilities=["project_setup", "frontend"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=900,  # 15 minutes
                output_variables={"project_structure": "structure.json"}
            ),
            "implement_components": WorkflowStep(
                id="implement_components",
                name="Implement UI Components",
                type=StepType.PARALLEL,
                description="Implement reusable UI components",
                depends_on=["setup_frontend_project"],
                parallel_steps=["create_base_components", "implement_forms", "create_layouts"],
                wait_for_all=True,
                estimated_duration=3600,  # 60 minutes
            ),
            "create_base_components": WorkflowStep(
                id="create_base_components",
                name="Create Base Components",
                type=StepType.TASK,
                description="Create basic UI components (buttons, inputs, etc.)",
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "components"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"base_components": "base_components.json"}
            ),
            "implement_forms": WorkflowStep(
                id="implement_forms",
                name="Implement Forms",
                type=StepType.TASK,
                description="Create form components with validation",
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "forms"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"form_components": "forms.json"}
            ),
            "create_layouts": WorkflowStep(
                id="create_layouts",
                name="Create Layouts",
                type=StepType.TASK,
                description="Create page layouts and navigation",
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "layout"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2100,  # 35 minutes
                output_variables={"layouts": "layouts.json"}
            ),
            "implement_pages": WorkflowStep(
                id="implement_pages",
                name="Implement Pages",
                type=StepType.TASK,
                description="Create application pages using components",
                depends_on=["implement_components"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "pages"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=3000,  # 50 minutes
                output_variables={"pages": "pages.json"}
            ),
            "implement_routing": WorkflowStep(
                id="implement_routing",
                name="Implement Routing",
                type=StepType.TASK,
                description="Setup client-side routing and navigation",
                depends_on=["implement_pages"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "routing"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"routing_config": "routing.json"}
            ),
            "add_styling": WorkflowStep(
                id="add_styling",
                name="Add Styling and Themes",
                type=StepType.TASK,
                description="Apply styling, themes, and responsive design",
                depends_on=["implement_routing"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "css", "design"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"styles": "styles.json"}
            ),
            "optimize_performance": WorkflowStep(
                id="optimize_performance",
                name="Optimize Performance",
                type=StepType.TASK,
                description="Optimize frontend performance and bundle size",
                depends_on=["add_styling"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "optimization"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"optimization_report": "optimization.json"}
            ),
            "test_ui": WorkflowStep(
                id="test_ui",
                name="Test User Interface",
                type=StepType.TASK,
                description="Run UI tests and accessibility checks",
                depends_on=["optimize_performance"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "ui_testing"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"ui_test_results": "ui_tests.json"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-ui",
            name="Greenfield UI Development",
            version="1.0.0",
            description="Frontend user interface development with components and styling",
            author="Aetherforge",
            tags={"greenfield", "frontend", "ui", "components"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=7200,  # 2 hours
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["analysis", "ui_design", "frontend", "testing"],
            project_types=["frontend", "spa", "web_ui"],
            total_duration_minutes=120  # 2 hours
        )

    def _create_brownfield_fullstack_template(self) -> WorkflowDefinition:
        """Create Brownfield Fullstack enhancement workflow template"""
        steps = {
            "analyze_existing_codebase": WorkflowStep(
                id="analyze_existing_codebase",
                name="Analyze Existing Codebase",
                type=StepType.TASK,
                description="Analyze existing codebase structure and identify enhancement opportunities",
                agent_requirements=AgentRequirement(
                    capabilities=["code_analysis", "refactoring"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"codebase_analysis": "analysis.json", "enhancement_plan": "plan.json"}
            ),
            "identify_dependencies": WorkflowStep(
                id="identify_dependencies",
                name="Identify Dependencies",
                type=StepType.TASK,
                description="Map dependencies and potential impact areas",
                depends_on=["analyze_existing_codebase"],
                agent_requirements=AgentRequirement(
                    capabilities=["dependency_analysis", "architecture"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"dependency_map": "dependencies.json"}
            ),
            "create_enhancement_plan": WorkflowStep(
                id="create_enhancement_plan",
                name="Create Enhancement Plan",
                type=StepType.TASK,
                description="Create detailed plan for enhancements with risk assessment",
                depends_on=["identify_dependencies"],
                agent_requirements=AgentRequirement(
                    capabilities=["planning", "risk_assessment"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"enhancement_strategy": "strategy.json"}
            ),
            "backup_existing_code": WorkflowStep(
                id="backup_existing_code",
                name="Backup Existing Code",
                type=StepType.TASK,
                description="Create backup of existing codebase",
                depends_on=["create_enhancement_plan"],
                agent_requirements=AgentRequirement(
                    capabilities=["version_control", "backup"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=600,  # 10 minutes
                output_variables={"backup_location": "backup.json"}
            ),
            "implement_backend_enhancements": WorkflowStep(
                id="implement_backend_enhancements",
                name="Implement Backend Enhancements",
                type=StepType.TASK,
                description="Enhance backend functionality while maintaining compatibility",
                depends_on=["backup_existing_code"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "refactoring"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=3600,  # 60 minutes
                output_variables={"backend_changes": "backend_changes.json"}
            ),
            "implement_frontend_enhancements": WorkflowStep(
                id="implement_frontend_enhancements",
                name="Implement Frontend Enhancements",
                type=StepType.TASK,
                description="Enhance frontend features and user experience",
                depends_on=["implement_backend_enhancements"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui_enhancement"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=3000,  # 50 minutes
                output_variables={"frontend_changes": "frontend_changes.json"}
            ),
            "update_tests": WorkflowStep(
                id="update_tests",
                name="Update and Add Tests",
                type=StepType.TASK,
                description="Update existing tests and add new tests for enhancements",
                depends_on=["implement_frontend_enhancements"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "test_maintenance"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"updated_tests": "tests.json"}
            ),
            "run_regression_tests": WorkflowStep(
                id="run_regression_tests",
                name="Run Regression Tests",
                type=StepType.TASK,
                description="Run comprehensive regression tests to ensure no breaking changes",
                depends_on=["update_tests"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "regression_testing"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"regression_results": "regression.json"},
                retry_config=RetryConfig(max_attempts=2, delay_seconds=60)
            ),
            "performance_testing": WorkflowStep(
                id="performance_testing",
                name="Performance Testing",
                type=StepType.TASK,
                description="Test performance impact of enhancements",
                depends_on=["run_regression_tests"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["performance_testing", "monitoring"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"performance_report": "performance.json"}
            ),
            "deploy_enhancements": WorkflowStep(
                id="deploy_enhancements",
                name="Deploy Enhancements",
                type=StepType.TASK,
                description="Deploy enhancements to staging/production environment",
                depends_on=["performance_testing"],
                condition=StepCondition(
                    variable="step.run_regression_tests.success",
                    operator=ConditionOperator.EQUALS,
                    value=True
                ),
                agent_requirements=AgentRequirement(
                    capabilities=["deployment", "devops"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"deployment_status": "deployment.json"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-fullstack",
            name="Brownfield Fullstack Enhancement",
            version="1.0.0",
            description="Enhancement of existing fullstack applications with careful impact analysis",
            author="Aetherforge",
            tags={"brownfield", "enhancement", "fullstack", "refactoring"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=10800,  # 3 hours
            parallel_execution=False,
            required_capabilities=["code_analysis", "refactoring", "backend", "frontend", "testing"],
            project_types=["enhancement", "refactoring", "legacy_modernization"],
            total_duration_minutes=180  # 3 hours
        )

    def _create_brownfield_service_template(self) -> WorkflowDefinition:
        """Create Brownfield Service enhancement workflow template"""
        steps = {
            "analyze_service_codebase": WorkflowStep(
                id="analyze_service_codebase",
                name="Analyze Service Codebase",
                type=StepType.TASK,
                description="Analyze existing service code and identify enhancement opportunities",
                agent_requirements=AgentRequirement(
                    capabilities=["code_analysis", "service_analysis"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"service_analysis": "service_analysis.json"}
            ),
            "plan_service_enhancements": WorkflowStep(
                id="plan_service_enhancements",
                name="Plan Service Enhancements",
                type=StepType.TASK,
                description="Create enhancement plan for service improvements",
                depends_on=["analyze_service_codebase"],
                agent_requirements=AgentRequirement(
                    capabilities=["planning", "architecture"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"enhancement_plan": "plan.json"}
            ),
            "enhance_service_logic": WorkflowStep(
                id="enhance_service_logic",
                name="Enhance Service Logic",
                type=StepType.TASK,
                description="Implement service logic enhancements",
                depends_on=["plan_service_enhancements"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "service_development"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"enhanced_logic": "logic.json"}
            ),
            "update_service_tests": WorkflowStep(
                id="update_service_tests",
                name="Update Service Tests",
                type=StepType.TASK,
                description="Update and add tests for enhanced service functionality",
                depends_on=["enhance_service_logic"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "service_testing"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"updated_tests": "tests.json"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-service",
            name="Brownfield Service Enhancement",
            version="1.0.0",
            description="Enhancement of existing backend services",
            author="Aetherforge",
            tags={"brownfield", "service", "backend", "enhancement"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600,  # 1 hour
            parallel_execution=False,
            required_capabilities=["code_analysis", "backend", "testing"],
            project_types=["service_enhancement", "api_improvement"],
            total_duration_minutes=60  # 1 hour
        )

    def _create_brownfield_ui_template(self) -> WorkflowDefinition:
        """Create Brownfield UI enhancement workflow template"""
        steps = {
            "analyze_ui_codebase": WorkflowStep(
                id="analyze_ui_codebase",
                name="Analyze UI Codebase",
                type=StepType.TASK,
                description="Analyze existing UI code and identify improvement areas",
                agent_requirements=AgentRequirement(
                    capabilities=["code_analysis", "ui_analysis"],
                    preferred_agents=["analyst_agent"]
                ),
                estimated_duration=1500,  # 25 minutes
                output_variables={"ui_analysis": "ui_analysis.json"}
            ),
            "plan_ui_improvements": WorkflowStep(
                id="plan_ui_improvements",
                name="Plan UI Improvements",
                type=StepType.TASK,
                description="Create plan for UI/UX improvements",
                depends_on=["analyze_ui_codebase"],
                agent_requirements=AgentRequirement(
                    capabilities=["ui_design", "planning"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=1200,  # 20 minutes
                output_variables={"improvement_plan": "ui_plan.json"}
            ),
            "enhance_ui_components": WorkflowStep(
                id="enhance_ui_components",
                name="Enhance UI Components",
                type=StepType.TASK,
                description="Improve existing UI components and add new ones",
                depends_on=["plan_ui_improvements"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui_development"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2700,  # 45 minutes
                output_variables={"enhanced_components": "components.json"}
            ),
            "update_ui_tests": WorkflowStep(
                id="update_ui_tests",
                name="Update UI Tests",
                type=StepType.TASK,
                description="Update and add tests for enhanced UI components",
                depends_on=["enhance_ui_components"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "ui_testing"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=1500,  # 25 minutes
                output_variables={"ui_tests": "ui_tests.json"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-ui",
            name="Brownfield UI Enhancement",
            version="1.0.0",
            description="Enhancement of existing user interfaces",
            author="Aetherforge",
            tags={"brownfield", "ui", "frontend", "enhancement"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600,  # 1 hour
            parallel_execution=False,
            required_capabilities=["code_analysis", "frontend", "ui_design", "testing"],
            project_types=["ui_enhancement", "frontend_improvement"],
            total_duration_minutes=60  # 1 hour
        )

    def _create_microservice_architecture_template(self) -> WorkflowDefinition:
        """Create Microservice Architecture workflow template"""
        steps = {
            "design_microservice_architecture": WorkflowStep(
                id="design_microservice_architecture",
                name="Design Microservice Architecture",
                type=StepType.TASK,
                description="Design overall microservice architecture and service boundaries",
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "microservices", "distributed_systems"],
                    preferred_agents=["architect_agent"]
                ),
                estimated_duration=3600,  # 60 minutes
                output_variables={"architecture_design": "architecture.json", "service_map": "services.json"}
            ),
            "setup_service_mesh": WorkflowStep(
                id="setup_service_mesh",
                name="Setup Service Mesh",
                type=StepType.TASK,
                description="Configure service mesh for inter-service communication",
                depends_on=["design_microservice_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["devops", "service_mesh", "networking"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"service_mesh_config": "mesh.json"}
            ),
            "implement_core_services": WorkflowStep(
                id="implement_core_services",
                name="Implement Core Services",
                type=StepType.PARALLEL,
                description="Implement core microservices in parallel",
                depends_on=["setup_service_mesh"],
                parallel_steps=["implement_user_service", "implement_auth_service", "implement_data_service"],
                wait_for_all=True,
                estimated_duration=5400,  # 90 minutes
            ),
            "implement_user_service": WorkflowStep(
                id="implement_user_service",
                name="Implement User Service",
                type=StepType.TASK,
                description="Implement user management microservice",
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "microservices", "user_management"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=3600,  # 60 minutes
                output_variables={"user_service": "user_service.json"}
            ),
            "implement_auth_service": WorkflowStep(
                id="implement_auth_service",
                name="Implement Auth Service",
                type=StepType.TASK,
                description="Implement authentication and authorization microservice",
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "security", "auth"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=4200,  # 70 minutes
                output_variables={"auth_service": "auth_service.json"}
            ),
            "implement_data_service": WorkflowStep(
                id="implement_data_service",
                name="Implement Data Service",
                type=StepType.TASK,
                description="Implement data management microservice",
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "database", "data_management"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=3600,  # 60 minutes
                output_variables={"data_service": "data_service.json"}
            ),
            "setup_api_gateway": WorkflowStep(
                id="setup_api_gateway",
                name="Setup API Gateway",
                type=StepType.TASK,
                description="Configure API gateway for external access",
                depends_on=["implement_core_services"],
                agent_requirements=AgentRequirement(
                    capabilities=["api_gateway", "networking", "security"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=1800,  # 30 minutes
                output_variables={"api_gateway_config": "gateway.json"}
            ),
            "implement_monitoring": WorkflowStep(
                id="implement_monitoring",
                name="Implement Distributed Monitoring",
                type=StepType.TASK,
                description="Setup distributed tracing and monitoring",
                depends_on=["setup_api_gateway"],
                agent_requirements=AgentRequirement(
                    capabilities=["monitoring", "observability", "devops"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"monitoring_setup": "monitoring.json"}
            ),
            "test_microservices": WorkflowStep(
                id="test_microservices",
                name="Test Microservices",
                type=StepType.TASK,
                description="Run integration and contract tests for microservices",
                depends_on=["implement_monitoring"],
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "integration_testing", "contract_testing"],
                    preferred_agents=["qa_agent"]
                ),
                estimated_duration=3000,  # 50 minutes
                output_variables={"test_results": "microservice_tests.json"}
            ),
            "deploy_microservices": WorkflowStep(
                id="deploy_microservices",
                name="Deploy Microservices",
                type=StepType.TASK,
                description="Deploy microservices to container orchestration platform",
                depends_on=["test_microservices"],
                condition=StepCondition(
                    variable="step.test_microservices.success",
                    operator=ConditionOperator.EQUALS,
                    value=True
                ),
                agent_requirements=AgentRequirement(
                    capabilities=["deployment", "kubernetes", "devops"],
                    preferred_agents=["developer_agent"]
                ),
                estimated_duration=2400,  # 40 minutes
                output_variables={"deployment_status": "deployment.json"}
            )
        }

        return WorkflowDefinition(
            id="microservice-architecture",
            name="Microservice Architecture Implementation",
            version="1.0.0",
            description="Complete microservice architecture with service mesh and monitoring",
            author="Aetherforge",
            tags={"microservices", "distributed", "architecture", "scalable"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=18000,  # 5 hours
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["architecture", "microservices", "backend", "devops", "testing"],
            project_types=["microservices", "distributed_system", "scalable_architecture"],
            total_duration_minutes=300  # 5 hours
        )

    def _create_game_prototype_template(self) -> WorkflowDefinition:
        """Create Game Prototype workflow template"""
        return WorkflowDefinition(
            id="game-prototype",
            name="Game Prototype Development",
            version="1.0.0",
            description="Rapid game prototype development",
            author="Aetherforge",
            tags={"game", "prototype", "rapid"},
            steps={},
            step_order=[],
            project_types=["game", "prototype"]
        )

    def _create_api_service_template(self) -> WorkflowDefinition:
        """Create API Service workflow template"""
        return WorkflowDefinition(
            id="api-service",
            name="API Service Development",
            version="1.0.0",
            description="RESTful API service development",
            author="Aetherforge",
            tags={"api", "service", "rest"},
            steps={},
            step_order=[],
            project_types=["api", "service"]
        )

    def _create_mobile_app_template(self) -> WorkflowDefinition:
        """Create Mobile App workflow template"""
        return WorkflowDefinition(
            id="mobile-app",
            name="Mobile App Development",
            version="1.0.0",
            description="Cross-platform mobile app development",
            author="Aetherforge",
            tags={"mobile", "app", "cross-platform"},
            steps={},
            step_order=[],
            project_types=["mobile", "app"]
        )

    def _create_desktop_app_template(self) -> WorkflowDefinition:
        """Create Desktop App workflow template"""
        return WorkflowDefinition(
            id="desktop-app",
            name="Desktop App Development",
            version="1.0.0",
            description="Cross-platform desktop application development",
            author="Aetherforge",
            tags={"desktop", "app", "cross-platform"},
            steps={},
            step_order=[],
            project_types=["desktop", "app"]
        )


class WorkflowExecutionEngine:
    """Enhanced BMAD workflow execution engine with parallel processing and dynamic agent assignment"""

    def __init__(self, orchestrator=None, pheromone_bus=None):
        self.orchestrator = orchestrator
        self.pheromone_bus = pheromone_bus
        self.active_executions: Dict[str, WorkflowExecution] = {}

        # Initialize components (will be defined later in the file)
        self.agent_pool = None
        self.condition_evaluator = None
        self.parallel_executor = None
        self.workflow_monitor = None
        self.persistence_manager = None

        # Execution state
        self.is_running = False
        self.max_concurrent_workflows = 5
        self.step_timeout_default = 300.0  # 5 minutes

        logger.info("WorkflowExecutionEngine initialized")

    def _initialize_components(self):
        """Initialize workflow engine components"""
        # These will be initialized after the classes are defined
        pass

    async def execute_workflow(
        self,
        workflow: WorkflowDefinition,
        variables: Optional[Dict[str, Any]] = None,
        execution_id: Optional[str] = None
    ) -> WorkflowExecution:
        """Execute a workflow with full BMAD support"""

        if not execution_id:
            execution_id = str(uuid.uuid4())

        # Create execution context
        execution = WorkflowExecution(
            id=execution_id,
            workflow_id=workflow.id,
            status=WorkflowStatus.PENDING,
            started_at=time.time(),
            total_steps=len(workflow.steps),
            variables={name: var for name, var in workflow.variables.items()}
        )

        # Add user-provided variables
        if variables:
            for name, value in variables.items():
                execution.variables[name] = WorkflowVariable(
                    name=name,
                    value=value,
                    type=type(value).__name__
                )

        # Initialize step status
        for step_id in workflow.steps:
            execution.step_status[step_id] = StepStatus.PENDING
            execution.step_attempts[step_id] = 0

        # Store execution
        self.active_executions[execution_id] = execution

        try:
            # Start monitoring
            self.workflow_monitor.start_monitoring(execution, workflow)

            # Save initial state
            await self.persistence_manager.save_execution_state(execution, workflow)

            # Execute workflow
            execution.status = WorkflowStatus.RUNNING
            await self._execute_workflow_steps(workflow, execution)

            # Finalize execution
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            logger.info(f"Workflow {workflow.id} completed successfully in {execution.execution_time:.2f}s")

        except WorkflowTimeoutError as e:
            execution.status = WorkflowStatus.TIMEOUT
            execution.last_error = str(e)
            logger.error(f"Workflow {workflow.id} timed out: {e}")

        except WorkflowExecutionError as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = str(e)
            execution.error_details = e.details
            logger.error(f"Workflow {workflow.id} failed: {e}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = str(e)
            logger.error(f"Unexpected error in workflow {workflow.id}: {e}")

        finally:
            # Stop monitoring
            self.workflow_monitor.stop_monitoring(execution_id)

            # Save final state
            await self.persistence_manager.save_execution_state(execution, workflow)

            # Clean up if completed
            if execution.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.TIMEOUT]:
                # Keep in active executions for a while for monitoring
                pass

        return execution

    async def _execute_workflow_steps(self, workflow: WorkflowDefinition, execution: WorkflowExecution):
        """Execute workflow steps with dependency resolution and parallel processing"""

        # Build dependency graph
        dependency_graph = self._build_dependency_graph(workflow)

        # Track step execution
        completed_steps = set()
        running_tasks = {}
        failed_steps = set()

        # Main execution loop
        while len(completed_steps) < len(workflow.steps):
            # Find ready steps (dependencies satisfied)
            ready_steps = self._find_ready_steps(
                workflow, dependency_graph, completed_steps, running_tasks, failed_steps
            )

            # Start new tasks for ready steps
            for step_id in ready_steps:
                if len(running_tasks) < workflow.max_concurrent_steps:
                    step = workflow.steps[step_id]

                    # Check step condition
                    if step.condition and not await self.condition_evaluator.evaluate_condition(
                        step.condition, execution, workflow
                    ):
                        logger.info(f"Step {step_id} skipped due to condition")
                        execution.step_status[step_id] = StepStatus.SKIPPED
                        execution.skipped_steps += 1
                        completed_steps.add(step_id)
                        continue

                    # Start step execution
                    task = asyncio.create_task(
                        self._execute_single_step(step, execution, workflow)
                    )
                    running_tasks[step_id] = task
                    execution.step_status[step_id] = StepStatus.RUNNING
                    execution.step_start_times[step_id] = time.time()

                    logger.info(f"Started step {step_id}")

            # Wait for at least one task to complete if we have running tasks
            if running_tasks:
                try:
                    done, pending_tasks = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=1.0  # Add timeout to prevent hanging
                    )

                    # Process completed tasks
                    for task in done:
                        # Find which step this task belongs to
                        step_id = None
                        for sid, t in running_tasks.items():
                            if t == task:
                                step_id = sid
                                break

                        if step_id:
                            try:
                                result = await task
                                execution.step_results[step_id] = result
                                execution.step_status[step_id] = StepStatus.COMPLETED
                                execution.completed_steps += 1
                                completed_steps.add(step_id)
                                execution.step_end_times[step_id] = time.time()

                                logger.info(f"Step {step_id} completed successfully")

                            except Exception as e:
                                step = workflow.steps[step_id]
                                if step.optional:
                                    execution.step_status[step_id] = StepStatus.OPTIONAL_FAILED
                                    execution.skipped_steps += 1
                                    completed_steps.add(step_id)
                                    logger.warning(f"Optional step {step_id} failed: {e}")
                                else:
                                    execution.step_status[step_id] = StepStatus.FAILED
                                    execution.failed_steps += 1
                                    failed_steps.add(step_id)
                                    logger.error(f"Step {step_id} failed: {e}")

                                    # Handle failure
                                    if step.on_failure:
                                        # Add failure handler step to ready steps
                                        pass

                            # Remove from running tasks
                            del running_tasks[step_id]

                except asyncio.TimeoutError:
                    # Check for global timeout
                    if time.time() - execution.started_at > workflow.global_timeout:
                        raise WorkflowTimeoutError(
                            f"Workflow global timeout ({workflow.global_timeout}s) exceeded",
                            workflow_id=workflow.id,
                            timeout_duration=workflow.global_timeout
                        )

            # Check if we're stuck (no progress possible)
            if not running_tasks and not ready_steps:
                remaining_steps = set(workflow.steps.keys()) - completed_steps - failed_steps
                if remaining_steps:
                    # Check if remaining steps have unsatisfied dependencies
                    unsatisfied_deps = []
                    for step_id in remaining_steps:
                        step = workflow.steps[step_id]
                        for dep in step.depends_on:
                            if dep in failed_steps:
                                unsatisfied_deps.append(f"{step_id} depends on failed step {dep}")

                    if unsatisfied_deps:
                        raise WorkflowExecutionError(
                            f"Workflow cannot proceed due to failed dependencies: {'; '.join(unsatisfied_deps)}",
                            workflow_id=workflow.id
                        )
                    else:
                        # This shouldn't happen if dependency resolution is correct
                        raise WorkflowExecutionError(
                            f"Workflow stuck with remaining steps: {remaining_steps}",
                            workflow_id=workflow.id
                        )
                break

        # Wait for any remaining tasks
        if running_tasks:
            await asyncio.gather(*running_tasks.values(), return_exceptions=True)

    def _build_dependency_graph(self, workflow: WorkflowDefinition) -> Dict[str, Set[str]]:
        """Build dependency graph for workflow steps"""
        graph = {}
        for step_id, step in workflow.steps.items():
            graph[step_id] = set(step.depends_on)
        return graph

    def _find_ready_steps(
        self,
        workflow: WorkflowDefinition,
        dependency_graph: Dict[str, Set[str]],
        completed_steps: Set[str],
        running_tasks: Dict[str, asyncio.Task],
        failed_steps: Set[str]
    ) -> List[str]:
        """Find steps that are ready to execute"""
        ready = []

        for step_id, step in workflow.steps.items():
            # Skip if already processed
            if (step_id in completed_steps or
                step_id in running_tasks or
                step_id in failed_steps):
                continue

            # Check if all dependencies are satisfied
            dependencies_satisfied = True
            for dep in step.depends_on:
                if dep not in completed_steps:
                    # Check if dependency failed and step is not optional
                    if dep in failed_steps and not step.optional:
                        dependencies_satisfied = False
                        break
                    # Dependency not completed yet
                    if dep not in failed_steps:
                        dependencies_satisfied = False
                        break

            if dependencies_satisfied:
                ready.append(step_id)

        return ready

    async def _execute_single_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> Dict[str, Any]:
        """Execute a single workflow step with retry logic and timeout"""

        max_attempts = 1
        if step.retry_config:
            max_attempts = step.retry_config.max_attempts

        last_exception = None

        for attempt in range(max_attempts):
            try:
                execution.step_attempts[step.id] = attempt + 1

                # Assign agent if needed
                if step.agent_requirements and not step.assigned_agent:
                    agent = await self.agent_pool.assign_agent(step.agent_requirements)
                    step.assigned_agent = agent.id if agent else None
                    execution.agent_assignments[step.id] = step.assigned_agent

                # Set timeout
                timeout = self.step_timeout_default
                if step.timeout_config:
                    timeout = step.timeout_config.execution_timeout

                # Execute step based on type
                if step.type == StepType.TASK:
                    result = await self._execute_task_step(step, execution, workflow, timeout)
                elif step.type == StepType.PARALLEL:
                    result = await self._execute_parallel_step(step, execution, workflow, timeout)
                elif step.type == StepType.LOOP:
                    result = await self._execute_loop_step(step, execution, workflow, timeout)
                elif step.type == StepType.CONDITION:
                    result = await self._execute_condition_step(step, execution, workflow, timeout)
                else:
                    result = await self._execute_custom_step(step, execution, workflow, timeout)

                # Update execution variables with step outputs
                if step.output_variables and result:
                    for var_name, result_key in step.output_variables.items():
                        if result_key in result:
                            execution.variables[var_name] = WorkflowVariable(
                                name=var_name,
                                value=result[result_key],
                                type=type(result[result_key]).__name__
                            )

                return result

            except asyncio.TimeoutError:
                last_exception = WorkflowExecutionError(
                    f"Step {step.id} timed out after {timeout}s",
                    workflow_id=workflow.id,
                    step_id=step.id
                )

            except Exception as e:
                last_exception = e

                # Check if we should retry
                if attempt < max_attempts - 1 and step.retry_config:
                    delay = step.retry_config.delay_seconds * (step.retry_config.backoff_multiplier ** attempt)
                    delay = min(delay, step.retry_config.max_delay_seconds)

                    logger.warning(f"Step {step.id} failed (attempt {attempt + 1}/{max_attempts}), retrying in {delay}s: {e}")
                    execution.step_status[step.id] = StepStatus.RETRYING
                    await asyncio.sleep(delay)
                    continue

        # All attempts failed
        if last_exception:
            raise last_exception
        else:
            raise WorkflowExecutionError(
                f"Step {step.id} failed after {max_attempts} attempts",
                workflow_id=workflow.id,
                step_id=step.id
            )

    async def _execute_task_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        timeout: float
    ) -> Dict[str, Any]:
        """Execute a task step"""

        if not step.assigned_agent:
            raise WorkflowExecutionError(
                f"No agent assigned for step {step.id}",
                workflow_id=workflow.id,
                step_id=step.id
            )

        # Prepare step context
        context = {
            "step_id": step.id,
            "step_name": step.name,
            "description": step.description,
            "parameters": step.parameters,
            "environment": step.environment,
            "workflow_variables": {name: var.value for name, var in execution.variables.items()},
            "previous_results": execution.step_results
        }

        # Execute via orchestrator if available
        if self.orchestrator:
            result = await asyncio.wait_for(
                self.orchestrator.execute_agent_task(
                    agent_id=step.assigned_agent,
                    task_type=step.command or "execute_step",
                    context=context
                ),
                timeout=timeout
            )
        else:
            # Mock execution for testing
            await asyncio.sleep(0.1)
            result = {
                "success": True,
                "output": f"Mock execution of step {step.id}",
                "execution_time": 0.1
            }

        return result

    async def _execute_parallel_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        timeout: float
    ) -> Dict[str, Any]:
        """Execute parallel steps"""

        if not step.parallel_steps:
            return {"success": True, "message": "No parallel steps to execute"}

        # Create tasks for parallel steps
        parallel_tasks = {}
        for parallel_step_id in step.parallel_steps:
            if parallel_step_id in workflow.steps:
                parallel_step = workflow.steps[parallel_step_id]
                task = asyncio.create_task(
                    self._execute_single_step(parallel_step, execution, workflow)
                )
                parallel_tasks[parallel_step_id] = task

        # Wait for completion
        if step.wait_for_all:
            # Wait for all tasks to complete
            results = await asyncio.gather(*parallel_tasks.values(), return_exceptions=True)

            # Process results
            parallel_results = {}
            for i, (step_id, task) in enumerate(parallel_tasks.items()):
                if isinstance(results[i], Exception):
                    if not workflow.steps[step_id].optional:
                        raise results[i]
                    else:
                        parallel_results[step_id] = {"success": False, "error": str(results[i])}
                else:
                    parallel_results[step_id] = results[i]

            return {"success": True, "parallel_results": parallel_results}
        else:
            # Wait for first completion
            done, pending = await asyncio.wait(
                parallel_tasks.values(),
                return_when=asyncio.FIRST_COMPLETED,
                timeout=timeout
            )

            # Cancel pending tasks
            for task in pending:
                task.cancel()

            # Return first result
            if done:
                first_task = next(iter(done))
                return await first_task
            else:
                raise WorkflowExecutionError(
                    f"Parallel step {step.id} timed out",
                    workflow_id=workflow.id,
                    step_id=step.id
                )

    async def _execute_loop_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        timeout: float
    ) -> Dict[str, Any]:
        """Execute loop step"""

        loop_results = []
        iteration = 0
        start_time = time.time()

        # Determine loop items
        if step.loop_items:
            items = step.loop_items
        else:
            # Use loop condition
            items = []
            while iteration < step.max_iterations:
                if step.loop_condition:
                    if not await self.condition_evaluator.evaluate_condition(
                        step.loop_condition, execution, workflow
                    ):
                        break
                items.append(iteration)
                iteration += 1

        # Execute loop iterations
        for item in items:
            if time.time() - start_time > timeout:
                raise WorkflowExecutionError(
                    f"Loop step {step.id} timed out after {timeout}s",
                    workflow_id=workflow.id,
                    step_id=step.id
                )

            # Set loop variable
            if step.loop_variable:
                execution.variables[step.loop_variable] = WorkflowVariable(
                    name=step.loop_variable,
                    value=item,
                    type=type(item).__name__
                )

            # Execute loop body (could be sub-steps)
            iteration_result = {
                "iteration": iteration,
                "item": item,
                "success": True
            }

            loop_results.append(iteration_result)
            iteration += 1

        return {
            "success": True,
            "iterations": len(loop_results),
            "results": loop_results
        }

    async def _execute_condition_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        timeout: float
    ) -> Dict[str, Any]:
        """Execute conditional step"""

        if not step.condition:
            return {"success": True, "condition_result": True}

        result = await self.condition_evaluator.evaluate_condition(
            step.condition, execution, workflow
        )

        return {
            "success": True,
            "condition_result": result,
            "condition_description": step.condition.description
        }

    async def _execute_custom_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        timeout: float
    ) -> Dict[str, Any]:
        """Execute custom step type"""

        # This can be extended for custom step types
        return {
            "success": True,
            "message": f"Custom step {step.id} executed",
            "step_type": step.type.value
        }

    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of workflow execution"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return None

        return {
            "execution_id": execution_id,
            "workflow_id": execution.workflow_id,
            "status": execution.status.value,
            "progress": execution.completed_steps / execution.total_steps if execution.total_steps > 0 else 0,
            "completed_steps": execution.completed_steps,
            "failed_steps": execution.failed_steps,
            "skipped_steps": execution.skipped_steps,
            "total_steps": execution.total_steps,
            "started_at": execution.started_at,
            "execution_time": time.time() - execution.started_at,
            "last_error": execution.last_error
        }

    def list_active_executions(self) -> List[Dict[str, Any]]:
        """Get status of all active executions"""
        active_executions = []

        for execution_id, execution in self.active_executions.items():
            monitoring_data = self.get_execution_monitoring_data(execution_id)
            if monitoring_data:
                active_executions.append(monitoring_data)

        return active_executions

    def get_execution_monitoring_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed monitoring data for execution"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return None

        return {
            "execution_id": execution_id,
            "workflow_id": execution.workflow_id,
            "status": execution.status.value,
            "progress": execution.completed_steps / execution.total_steps if execution.total_steps > 0 else 0,
            "step_status": {step_id: status.value for step_id, status in execution.step_status.items()},
            "agent_assignments": execution.agent_assignments,
            "step_results": execution.step_results,
            "variables": {name: var.value for name, var in execution.variables.items()},
            "performance": {
                "execution_time": time.time() - execution.started_at,
                "step_times": {
                    step_id: execution.step_end_times.get(step_id, time.time()) - start_time
                    for step_id, start_time in execution.step_start_times.items()
                },
                "agent_response_times": execution.agent_response_times
            }
        }

    async def pause_execution(self, execution_id: str) -> bool:
        """Pause workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.RUNNING:
            execution.status = WorkflowStatus.PAUSED
            logger.info(f"Paused workflow execution {execution_id}")
            return True
        return False

    async def resume_execution(self, execution_id: str) -> bool:
        """Resume paused workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.PAUSED:
            execution.status = WorkflowStatus.RUNNING
            logger.info(f"Resumed workflow execution {execution_id}")
            return True
        return False

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = time.time()
            logger.info(f"Cancelled workflow execution {execution_id}")
            return True
        return False


class ConditionEvaluator:
    """Evaluates workflow step conditions with support for complex logic"""

    def __init__(self):
        self.operators = {
            ConditionOperator.EQUALS: self._equals,
            ConditionOperator.NOT_EQUALS: self._not_equals,
            ConditionOperator.GREATER_THAN: self._greater_than,
            ConditionOperator.LESS_THAN: self._less_than,
            ConditionOperator.GREATER_EQUAL: self._greater_equal,
            ConditionOperator.LESS_EQUAL: self._less_equal,
            ConditionOperator.CONTAINS: self._contains,
            ConditionOperator.NOT_CONTAINS: self._not_contains,
            ConditionOperator.REGEX_MATCH: self._regex_match,
            ConditionOperator.EXISTS: self._exists,
            ConditionOperator.NOT_EXISTS: self._not_exists,
            ConditionOperator.IN_LIST: self._in_list,
            ConditionOperator.NOT_IN_LIST: self._not_in_list,
            ConditionOperator.STARTS_WITH: self._starts_with,
            ConditionOperator.ENDS_WITH: self._ends_with,
            ConditionOperator.IS_EMPTY: self._is_empty,
            ConditionOperator.IS_NOT_EMPTY: self._is_not_empty,
            ConditionOperator.AND: self._and,
            ConditionOperator.OR: self._or,
            ConditionOperator.NOT: self._not
        }

    async def evaluate_condition(
        self,
        condition: StepCondition,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Evaluate a step condition"""
        try:
            result = await self._evaluate_single_condition(condition, execution, workflow)
            return not result if condition.negate else result
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

    async def _evaluate_single_condition(
        self,
        condition: StepCondition,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Evaluate a single condition"""

        # Handle logical operators
        if condition.operator in [ConditionOperator.AND, ConditionOperator.OR, ConditionOperator.NOT]:
            return await self._evaluate_logical_condition(condition, execution, workflow)

        # Get variable value
        variable_value = self._get_variable_value(condition.variable, execution, workflow)

        # Apply operator
        operator_func = self.operators.get(condition.operator)
        if operator_func:
            return operator_func(variable_value, condition.value)
        else:
            logger.warning(f"Unknown condition operator: {condition.operator}")
            return False

    async def _evaluate_logical_condition(
        self,
        condition: StepCondition,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Evaluate logical condition (AND, OR, NOT)"""

        if condition.operator == ConditionOperator.AND:
            for sub_condition in condition.sub_conditions:
                if not await self.evaluate_condition(sub_condition, execution, workflow):
                    return False
            return True

        elif condition.operator == ConditionOperator.OR:
            for sub_condition in condition.sub_conditions:
                if await self.evaluate_condition(sub_condition, execution, workflow):
                    return True
            return False

        elif condition.operator == ConditionOperator.NOT:
            if condition.sub_conditions:
                return not await self.evaluate_condition(condition.sub_conditions[0], execution, workflow)
            return True

        return False

    def _get_variable_value(
        self,
        variable_name: str,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> Any:
        """Get variable value from execution context"""

        # Handle special variable references
        if variable_name.startswith('step.'):
            # Step result reference: step.step_id.property
            parts = variable_name.split('.')
            if len(parts) >= 2:
                step_id = parts[1]
                if step_id in execution.step_results:
                    result = execution.step_results[step_id]
                    if len(parts) == 2:
                        return result
                    elif len(parts) == 3:
                        property_name = parts[2]
                        if isinstance(result, dict) and property_name in result:
                            return result[property_name]
                        elif hasattr(result, property_name):
                            return getattr(result, property_name)
            return None

        elif variable_name.startswith('env.'):
            # Environment variable reference
            env_var = variable_name[4:]  # Remove 'env.' prefix
            return os.environ.get(env_var)

        else:
            # Regular workflow variable
            if variable_name in execution.variables:
                return execution.variables[variable_name].value
            elif variable_name in workflow.variables:
                return workflow.variables[variable_name].value

        return None

    # Operator implementations
    def _equals(self, value1: Any, value2: Any) -> bool:
        return value1 == value2

    def _not_equals(self, value1: Any, value2: Any) -> bool:
        return value1 != value2

    def _greater_than(self, value1: Any, value2: Any) -> bool:
        try:
            return float(value1) > float(value2)
        except (ValueError, TypeError):
            return str(value1) > str(value2)

    def _less_than(self, value1: Any, value2: Any) -> bool:
        try:
            return float(value1) < float(value2)
        except (ValueError, TypeError):
            return str(value1) < str(value2)

    def _greater_equal(self, value1: Any, value2: Any) -> bool:
        try:
            return float(value1) >= float(value2)
        except (ValueError, TypeError):
            return str(value1) >= str(value2)

    def _less_equal(self, value1: Any, value2: Any) -> bool:
        try:
            return float(value1) <= float(value2)
        except (ValueError, TypeError):
            return str(value1) <= str(value2)

    def _contains(self, value1: Any, value2: Any) -> bool:
        try:
            return str(value2) in str(value1)
        except TypeError:
            return False

    def _not_contains(self, value1: Any, value2: Any) -> bool:
        return not self._contains(value1, value2)

    def _regex_match(self, value1: Any, value2: Any) -> bool:
        try:
            import re
            return bool(re.search(str(value2), str(value1)))
        except Exception:
            return False

    def _exists(self, value1: Any, value2: Any) -> bool:
        return value1 is not None

    def _not_exists(self, value1: Any, value2: Any) -> bool:
        return value1 is None

    def _in_list(self, value1: Any, value2: Any) -> bool:
        try:
            if isinstance(value2, list):
                return value1 in value2
            return False
        except TypeError:
            return False

    def _not_in_list(self, value1: Any, value2: Any) -> bool:
        return not self._in_list(value1, value2)

    def _starts_with(self, value1: Any, value2: Any) -> bool:
        try:
            return str(value1).startswith(str(value2))
        except (AttributeError, TypeError):
            return False

    def _ends_with(self, value1: Any, value2: Any) -> bool:
        try:
            return str(value1).endswith(str(value2))
        except (AttributeError, TypeError):
            return False

    def _is_empty(self, value1: Any, value2: Any) -> bool:
        if value1 is None:
            return True
        if isinstance(value1, (str, list, dict)):
            return len(value1) == 0
        return False

    def _is_not_empty(self, value1: Any, value2: Any) -> bool:
        return not self._is_empty(value1, value2)

    def _and(self, value1: Any, value2: Any) -> bool:
        # This should not be called directly for logical AND
        return bool(value1) and bool(value2)

    def _or(self, value1: Any, value2: Any) -> bool:
        # This should not be called directly for logical OR
        return bool(value1) or bool(value2)

    def _not(self, value1: Any, value2: Any) -> bool:
        # This should not be called directly for logical NOT
        return not bool(value1)


class AgentPool:
    """Manages agent assignment and load balancing for workflow steps"""

    def __init__(self):
        self.available_agents: Dict[str, Dict[str, Any]] = {}
        self.agent_loads: Dict[str, float] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        self.agent_performance: Dict[str, float] = {}
        self.assignment_history: List[Dict[str, Any]] = []

    async def assign_agent(self, requirements: AgentRequirement) -> Optional[Dict[str, Any]]:
        """Assign an agent based on requirements and selection strategy"""

        # Filter agents by capabilities
        candidate_agents = self._filter_by_capabilities(requirements.capabilities)

        # Filter by performance requirements
        candidate_agents = self._filter_by_performance(candidate_agents, requirements.min_performance)

        # Filter by load requirements
        candidate_agents = self._filter_by_load(candidate_agents, requirements.max_load)

        # Apply exclusions
        candidate_agents = [
            agent for agent in candidate_agents
            if agent['id'] not in requirements.excluded_agents
        ]

        # Apply preferences
        preferred_candidates = [
            agent for agent in candidate_agents
            if agent['id'] in requirements.preferred_agents
        ]

        if preferred_candidates:
            candidate_agents = preferred_candidates

        if not candidate_agents:
            logger.warning(f"No agents available matching requirements: {requirements}")
            return None

        # Select agent based on strategy
        selected_agent = self._select_agent_by_strategy(candidate_agents, requirements.selection_strategy)

        if selected_agent:
            # Update agent load
            self.agent_loads[selected_agent['id']] = self.agent_loads.get(selected_agent['id'], 0) + 0.1

            # Record assignment
            self.assignment_history.append({
                'agent_id': selected_agent['id'],
                'timestamp': time.time(),
                'requirements': asdict(requirements)
            })

        return selected_agent

    def _filter_by_capabilities(self, required_capabilities: List[str]) -> List[Dict[str, Any]]:
        """Filter agents by required capabilities"""
        if not required_capabilities:
            return list(self.available_agents.values())

        matching_agents = []
        for agent_id, agent_info in self.available_agents.items():
            agent_caps = self.agent_capabilities.get(agent_id, [])
            if all(cap in agent_caps for cap in required_capabilities):
                matching_agents.append(agent_info)

        return matching_agents

    def _filter_by_performance(self, agents: List[Dict[str, Any]], min_performance: float) -> List[Dict[str, Any]]:
        """Filter agents by minimum performance requirement"""
        if min_performance <= 0:
            return agents

        return [
            agent for agent in agents
            if self.agent_performance.get(agent['id'], 1.0) >= min_performance
        ]

    def _filter_by_load(self, agents: List[Dict[str, Any]], max_load: float) -> List[Dict[str, Any]]:
        """Filter agents by maximum load requirement"""
        return [
            agent for agent in agents
            if self.agent_loads.get(agent['id'], 0.0) <= max_load
        ]

    def _select_agent_by_strategy(
        self,
        candidates: List[Dict[str, Any]],
        strategy: AgentSelectionStrategy
    ) -> Optional[Dict[str, Any]]:
        """Select agent based on selection strategy"""

        if not candidates:
            return None

        if strategy == AgentSelectionStrategy.ROUND_ROBIN:
            # Simple round-robin based on assignment history
            assignment_counts = {}
            for assignment in self.assignment_history:
                agent_id = assignment['agent_id']
                assignment_counts[agent_id] = assignment_counts.get(agent_id, 0) + 1

            # Select agent with fewest assignments
            min_assignments = min(
                assignment_counts.get(agent['id'], 0) for agent in candidates
            )
            candidates_with_min = [
                agent for agent in candidates
                if assignment_counts.get(agent['id'], 0) == min_assignments
            ]
            return candidates_with_min[0]

        elif strategy == AgentSelectionStrategy.LEAST_LOADED:
            # Select agent with lowest current load
            return min(candidates, key=lambda agent: self.agent_loads.get(agent['id'], 0.0))

        elif strategy == AgentSelectionStrategy.CAPABILITY_MATCH:
            # Select agent with best capability match (most capabilities)
            return max(candidates, key=lambda agent: len(self.agent_capabilities.get(agent['id'], [])))

        elif strategy == AgentSelectionStrategy.PRIORITY_BASED:
            # Select agent with highest priority (performance score)
            return max(candidates, key=lambda agent: self.agent_performance.get(agent['id'], 0.0))

        elif strategy == AgentSelectionStrategy.RANDOM:
            import random
            return random.choice(candidates)

        else:
            # Default to first available
            return candidates[0]

    def register_agent(
        self,
        agent_id: str,
        capabilities: List[str],
        performance_score: float = 1.0
    ):
        """Register an agent with the pool"""
        self.available_agents[agent_id] = {
            'id': agent_id,
            'capabilities': capabilities,
            'performance': performance_score,
            'registered_at': time.time()
        }
        self.agent_capabilities[agent_id] = capabilities
        self.agent_performance[agent_id] = performance_score
        self.agent_loads[agent_id] = 0.0

        logger.info(f"Registered agent {agent_id} with capabilities: {capabilities}")

    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the pool"""
        self.available_agents.pop(agent_id, None)
        self.agent_capabilities.pop(agent_id, None)
        self.agent_performance.pop(agent_id, None)
        self.agent_loads.pop(agent_id, None)

        logger.info(f"Unregistered agent {agent_id}")

    def update_agent_load(self, agent_id: str, load_delta: float):
        """Update agent load (positive to increase, negative to decrease)"""
        if agent_id in self.agent_loads:
            self.agent_loads[agent_id] = max(0.0, self.agent_loads[agent_id] + load_delta)

    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents in the pool"""
        return {
            'total_agents': len(self.available_agents),
            'agents': [
                {
                    'id': agent_id,
                    'capabilities': self.agent_capabilities.get(agent_id, []),
                    'performance': self.agent_performance.get(agent_id, 0.0),
                    'current_load': self.agent_loads.get(agent_id, 0.0),
                    'available': self.agent_loads.get(agent_id, 0.0) < 1.0
                }
                for agent_id in self.available_agents
            ]
        }


class ParallelExecutor:
    """Handles parallel execution of workflow steps with resource management"""

    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.resource_manager = ResourceManager()

    async def execute_parallel_steps(
        self,
        steps: List[WorkflowStep],
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> Dict[str, Any]:
        """Execute multiple steps in parallel"""

        # Check resource availability
        if not self.resource_manager.can_allocate_resources(len(steps)):
            raise WorkflowExecutionError(
                f"Insufficient resources for parallel execution of {len(steps)} steps",
                workflow_id=workflow.id
            )

        # Create tasks for each step
        tasks = {}
        for step in steps:
            task_id = f"{execution.id}_{step.id}"
            # This would integrate with the main execution engine
            # For now, we'll create a placeholder
            tasks[step.id] = asyncio.create_task(
                self._execute_step_async(step, execution, workflow)
            )
            self.active_tasks[task_id] = tasks[step.id]

        try:
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks.values(), return_exceptions=True)

            # Process results
            step_results = {}
            for i, step in enumerate(steps):
                if isinstance(results[i], Exception):
                    if not step.optional:
                        raise results[i]
                    step_results[step.id] = {"success": False, "error": str(results[i])}
                else:
                    step_results[step.id] = results[i]

            return {"success": True, "step_results": step_results}

        finally:
            # Clean up tasks
            for step in steps:
                task_id = f"{execution.id}_{step.id}"
                self.active_tasks.pop(task_id, None)

    async def _execute_step_async(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> Dict[str, Any]:
        """Execute a single step asynchronously"""
        # This is a placeholder - would integrate with main execution engine
        await asyncio.sleep(0.1)  # Simulate work
        return {"success": True, "step_id": step.id}

    def get_active_tasks(self) -> Dict[str, str]:
        """Get currently active parallel tasks"""
        return {task_id: "running" for task_id in self.active_tasks}

    def cancel_all_tasks(self):
        """Cancel all active parallel tasks"""
        for task in self.active_tasks.values():
            task.cancel()
        self.active_tasks.clear()


class ResourceManager:
    """Manages system resources for workflow execution"""

    def __init__(self):
        self.max_cpu_usage = 80.0  # Maximum CPU usage percentage
        self.max_memory_usage = 80.0  # Maximum memory usage percentage
        self.max_concurrent_tasks = 10
        self.current_tasks = 0
        self.cpu_usage = 0.0
        self.memory_usage = 0.0
        self.last_update = 0.0

    def can_allocate_resources(self, num_tasks: int) -> bool:
        """Check if resources can be allocated for the given number of tasks"""
        self.update_availability()

        # Check task limit
        if self.current_tasks + num_tasks > self.max_concurrent_tasks:
            return False

        # Check CPU usage
        if self.cpu_usage > self.max_cpu_usage:
            return False

        # Check memory usage
        if self.memory_usage > self.max_memory_usage:
            return False

        return True

    def allocate_resources(self, num_tasks: int) -> bool:
        """Allocate resources for tasks"""
        if self.can_allocate_resources(num_tasks):
            self.current_tasks += num_tasks
            return True
        return False

    def release_resources(self, num_tasks: int):
        """Release allocated resources"""
        self.current_tasks = max(0, self.current_tasks - num_tasks)

    def update_availability(self):
        """Update resource availability based on system monitoring"""
        try:
            import psutil

            # Validate current state
            if not hasattr(self, 'cpu_usage'):
                self.cpu_usage = 0.0
            if not hasattr(self, 'memory_usage'):
                self.memory_usage = 0.0

            # Update only if enough time has passed
            current_time = time.time()
            if current_time - self.last_update < 5.0:  # Update every 5 seconds
                return

            self.cpu_usage = psutil.cpu_percent(interval=0.1)
            self.memory_usage = psutil.virtual_memory().percent
            self.last_update = current_time

        except ImportError:
            # psutil not available, use conservative estimates
            self.cpu_usage = min(50.0, self.current_tasks * 5.0)
            self.memory_usage = min(50.0, self.current_tasks * 3.0)
            self.last_update = time.time()
        except Exception as e:
            logger.warning(f"Error updating resource availability: {e}")

    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource status"""
        self.update_availability()
        return {
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "current_tasks": self.current_tasks,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "available_task_slots": max(0, self.max_concurrent_tasks - self.current_tasks),
            "can_allocate_more": self.can_allocate_resources(1)
        }


class WorkflowMonitor:
    """Monitors workflow execution and provides real-time updates"""

    def __init__(self):
        self.monitored_executions: Dict[str, Dict[str, Any]] = {}
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        self.update_callbacks: List[Callable] = []

    def start_monitoring(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Start monitoring a workflow execution"""
        execution_id = execution.id

        self.monitored_executions[execution_id] = {
            "execution": execution,
            "workflow": workflow,
            "start_time": time.time(),
            "last_update": time.time(),
            "update_count": 0
        }

        # Start monitoring task
        task = asyncio.create_task(self._monitor_execution(execution_id))
        self.monitoring_tasks[execution_id] = task

        logger.info(f"Started monitoring workflow execution {execution_id}")

    def stop_monitoring(self, execution_id: str):
        """Stop monitoring a workflow execution"""
        # Cancel monitoring task
        if execution_id in self.monitoring_tasks:
            self.monitoring_tasks[execution_id].cancel()
            del self.monitoring_tasks[execution_id]

        # Remove from monitored executions
        self.monitored_executions.pop(execution_id, None)

        logger.info(f"Stopped monitoring workflow execution {execution_id}")

    async def _monitor_execution(self, execution_id: str):
        """Monitor a single execution"""
        try:
            while execution_id in self.monitored_executions:
                monitoring_data = self.monitored_executions[execution_id]
                execution = monitoring_data["execution"]
                workflow = monitoring_data["workflow"]

                # Update monitoring data
                current_time = time.time()
                monitoring_data["last_update"] = current_time
                monitoring_data["update_count"] += 1

                # Create update data
                update_data = {
                    "execution_id": execution_id,
                    "workflow_id": workflow.id,
                    "status": execution.status.value,
                    "progress": execution.completed_steps / execution.total_steps if execution.total_steps > 0 else 0,
                    "completed_steps": execution.completed_steps,
                    "failed_steps": execution.failed_steps,
                    "total_steps": execution.total_steps,
                    "execution_time": current_time - execution.started_at,
                    "step_status": {step_id: status.value for step_id, status in execution.step_status.items()},
                    "timestamp": current_time
                }

                # Send updates to callbacks
                for callback in self.update_callbacks:
                    try:
                        await callback(update_data)
                    except Exception as e:
                        logger.error(f"Error in monitoring callback: {e}")

                # Send pheromone update if available
                self._send_pheromone_update(execution, workflow)

                # Check if execution is complete
                if execution.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]:
                    break

                # Wait before next update
                await asyncio.sleep(2.0)  # Update every 2 seconds

        except asyncio.CancelledError:
            logger.info(f"Monitoring cancelled for execution {execution_id}")
        except Exception as e:
            logger.error(f"Error monitoring execution {execution_id}: {e}")

    def add_update_callback(self, callback: Callable):
        """Add a callback for monitoring updates"""
        self.update_callbacks.append(callback)

    def remove_update_callback(self, callback: Callable):
        """Remove a monitoring update callback"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of all monitored executions"""
        return {
            "total_monitored": len(self.monitored_executions),
            "executions": [
                {
                    "execution_id": execution_id,
                    "workflow_id": data["workflow"].id,
                    "status": data["execution"].status.value,
                    "monitoring_duration": time.time() - data["start_time"],
                    "update_count": data["update_count"]
                }
                for execution_id, data in self.monitored_executions.items()
            ]
        }

    def _send_pheromone_update(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Send monitoring update via pheromone system"""
        try:
            update_data = {
                "type": "workflow_monitoring_update",
                "execution_id": self.execution_id,
                "workflow_id": self.workflow_id,
                "status": execution.status.value,
                "progress": execution.completed_steps / execution.total_steps if execution.total_steps > 0 else 0,
                "timestamp": time.time()
            }

            # This would integrate with the pheromone bus
            # For now, just log the update
            logger.debug(f"Pheromone update: {update_data}")

        except Exception as e:
            logger.error(f"Error sending pheromone update: {e}")


class WorkflowPersistenceManager:
    """Manages workflow execution state persistence and recovery"""

    def __init__(self, storage_path: str = "workflow_storage"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)

    async def save_execution_state(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Save workflow execution state to disk"""
        try:
            execution_file = self.storage_path / f"execution_{execution.id}.json"
            workflow_file = self.storage_path / f"workflow_{workflow.id}.json"

            # Save execution state
            execution_data = {
                "id": execution.id,
                "workflow_id": execution.workflow_id,
                "status": execution.status.value,
                "started_at": execution.started_at,
                "completed_at": execution.completed_at,
                "variables": {name: {"value": var.value, "type": var.type} for name, var in execution.variables.items()},
                "step_results": execution.step_results,
                "step_status": {step_id: status.value for step_id, status in execution.step_status.items()},
                "step_attempts": execution.step_attempts,
                "agent_assignments": execution.agent_assignments,
                "total_steps": execution.total_steps,
                "completed_steps": execution.completed_steps,
                "failed_steps": execution.failed_steps,
                "skipped_steps": execution.skipped_steps,
                "step_start_times": execution.step_start_times,
                "step_end_times": execution.step_end_times,
                "last_error": execution.last_error,
                "error_details": execution.error_details,
                "execution_time": execution.execution_time,
                "agent_response_times": execution.agent_response_times
            }

            with open(execution_file, 'w') as f:
                json.dump(execution_data, f, indent=2, default=str)

            # Save workflow definition
            workflow_data = {
                "id": workflow.id,
                "name": workflow.name,
                "version": workflow.version,
                "description": workflow.description,
                "author": workflow.author,
                "created_at": workflow.created_at,
                "tags": list(workflow.tags),
                "variables": {name: asdict(var) for name, var in workflow.variables.items()},
                "steps": {step_id: self._serialize_step(step) for step_id, step in workflow.steps.items()},
                "step_order": workflow.step_order,
                "global_timeout": workflow.global_timeout,
                "parallel_execution": workflow.parallel_execution,
                "max_concurrent_steps": workflow.max_concurrent_steps,
                "required_capabilities": workflow.required_capabilities,
                "preferred_agents": workflow.preferred_agents,
                "agent_selection_strategy": workflow.agent_selection_strategy.value,
                "project_types": workflow.project_types,
                "total_duration_minutes": workflow.total_duration_minutes
            }

            with open(workflow_file, 'w') as f:
                json.dump(workflow_data, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error saving execution state: {e}")

    def _serialize_step(self, step: WorkflowStep) -> Dict[str, Any]:
        """Serialize a workflow step to dictionary"""
        step_data = {
            "id": step.id,
            "name": step.name,
            "type": step.type.value,
            "description": step.description,
            "command": step.command,
            "parameters": step.parameters,
            "environment": step.environment,
            "depends_on": step.depends_on,
            "assigned_agent": step.assigned_agent,
            "optional": step.optional,
            "parallel_steps": step.parallel_steps,
            "wait_for_all": step.wait_for_all,
            "loop_variable": step.loop_variable,
            "loop_items": step.loop_items,
            "max_iterations": step.max_iterations,
            "output_variables": step.output_variables,
            "capture_output": step.capture_output,
            "tags": list(step.tags),
            "priority": step.priority,
            "estimated_duration": step.estimated_duration,
            "on_failure": step.on_failure,
            "on_success": step.on_success
        }

        # Serialize complex objects
        if step.condition:
            step_data["condition"] = step.condition.to_dict()

        if step.agent_requirements:
            step_data["agent_requirements"] = asdict(step.agent_requirements)

        if step.retry_config:
            step_data["retry_config"] = asdict(step.retry_config)

        if step.timeout_config:
            step_data["timeout_config"] = asdict(step.timeout_config)

        if step.loop_condition:
            step_data["loop_condition"] = step.loop_condition.to_dict()

        return step_data

    async def load_execution_state(self, execution_id: str) -> Optional[Tuple[WorkflowExecution, WorkflowDefinition]]:
        """Load workflow execution state from disk"""
        try:
            execution_file = self.storage_path / f"execution_{execution_id}.json"

            if not execution_file.exists():
                return None

            with open(execution_file, 'r') as f:
                execution_data = json.load(f)

            # Load workflow definition
            workflow_id = execution_data["workflow_id"]
            workflow_file = self.storage_path / f"workflow_{workflow_id}.json"

            if not workflow_file.exists():
                logger.error(f"Workflow file not found for execution {execution_id}")
                return None

            with open(workflow_file, 'r') as f:
                workflow_data = json.load(f)

            # Reconstruct execution
            execution = self._deserialize_execution(execution_data)
            workflow = self._deserialize_workflow(workflow_data)

            return execution, workflow

        except Exception as e:
            logger.error(f"Error loading execution state: {e}")
            return None

    def _deserialize_execution(self, data: Dict[str, Any]) -> WorkflowExecution:
        """Deserialize execution from dictionary"""
        execution = WorkflowExecution(
            id=data["id"],
            workflow_id=data["workflow_id"],
            status=WorkflowStatus(data["status"]),
            started_at=data["started_at"],
            completed_at=data.get("completed_at"),
            total_steps=data["total_steps"],
            completed_steps=data["completed_steps"],
            failed_steps=data["failed_steps"],
            skipped_steps=data["skipped_steps"],
            step_start_times=data["step_start_times"],
            step_end_times=data["step_end_times"],
            last_error=data.get("last_error"),
            error_details=data.get("error_details", {}),
            execution_time=data.get("execution_time", 0.0),
            agent_response_times=data.get("agent_response_times", {}),
            step_results=data["step_results"],
            step_attempts=data["step_attempts"],
            agent_assignments=data["agent_assignments"]
        )

        # Reconstruct variables
        for name, var_data in data["variables"].items():
            execution.variables[name] = WorkflowVariable(
                name=name,
                value=var_data["value"],
                type=var_data["type"]
            )

        # Reconstruct step status
        for step_id, status_str in data["step_status"].items():
            execution.step_status[step_id] = StepStatus(status_str)

        return execution

    def _deserialize_workflow(self, data: Dict[str, Any]) -> WorkflowDefinition:
        """Deserialize workflow from dictionary"""
        # This is a simplified version - full implementation would reconstruct all objects
        workflow = WorkflowDefinition(
            id=data["id"],
            name=data["name"],
            version=data["version"],
            description=data["description"],
            author=data["author"],
            created_at=data["created_at"],
            tags=set(data["tags"]),
            step_order=data["step_order"],
            global_timeout=data["global_timeout"],
            parallel_execution=data["parallel_execution"],
            max_concurrent_steps=data["max_concurrent_steps"],
            required_capabilities=data["required_capabilities"],
            preferred_agents=data["preferred_agents"],
            agent_selection_strategy=AgentSelectionStrategy(data["agent_selection_strategy"]),
            project_types=data["project_types"],
            total_duration_minutes=data["total_duration_minutes"]
        )

        # Reconstruct variables and steps would go here
        # For brevity, using simplified reconstruction

        return workflow

    def list_saved_executions(self) -> List[Dict[str, Any]]:
        """List all saved execution states"""
        executions = []

        for execution_file in self.storage_path.glob("execution_*.json"):
            try:
                with open(execution_file, 'r') as f:
                    data = json.load(f)

                executions.append({
                    "execution_id": data["id"],
                    "workflow_id": data["workflow_id"],
                    "status": data["status"],
                    "started_at": data["started_at"],
                    "completed_at": data.get("completed_at"),
                    "file_path": str(execution_file)
                })

            except Exception as e:
                logger.error(f"Error reading execution file {execution_file}: {e}")

        return executions

    def cleanup_old_executions(self, max_age_days: int = 30):
        """Clean up old execution files"""
        cutoff_time = time.time() - (max_age_days * 24 * 3600)

        for execution_file in self.storage_path.glob("execution_*.json"):
            try:
                if execution_file.stat().st_mtime < cutoff_time:
                    execution_file.unlink()
                    logger.info(f"Cleaned up old execution file: {execution_file}")
            except Exception as e:
                logger.error(f"Error cleaning up execution file {execution_file}: {e}")


# Update WorkflowExecutionEngine to initialize components after classes are defined
def _initialize_workflow_engine_components(engine: WorkflowExecutionEngine):
    """Initialize workflow engine components after all classes are defined"""
    engine.agent_pool = AgentPool()
    engine.condition_evaluator = ConditionEvaluator()
    engine.parallel_executor = ParallelExecutor()
    engine.workflow_monitor = WorkflowMonitor()
    engine.persistence_manager = WorkflowPersistenceManager()

# Monkey patch the initialization method
WorkflowExecutionEngine._initialize_components = lambda self: _initialize_workflow_engine_components(self)


# Factory functions for easy access
def create_workflow_parser() -> WorkflowYAMLParser:
    """Create and return a configured WorkflowYAMLParser instance"""
    return WorkflowYAMLParser()

def create_template_manager() -> WorkflowTemplateManager:
    """Create and return a configured WorkflowTemplateManager instance"""
    return WorkflowTemplateManager()

def create_execution_engine(orchestrator=None, pheromone_bus=None) -> WorkflowExecutionEngine:
    """Create and return a configured WorkflowExecutionEngine instance"""
    engine = WorkflowExecutionEngine(orchestrator=orchestrator, pheromone_bus=pheromone_bus)
    _initialize_workflow_engine_components(engine)
    return engine


# Example usage and testing
if __name__ == "__main__":
    async def main():
        # Create workflow engine components
        parser = create_workflow_parser()
        template_manager = create_template_manager()
        execution_engine = create_execution_engine()

        # List available templates
        templates = template_manager.list_templates()
        print(f"Available templates: {len(templates)}")
        for template in templates:
            print(f"- {template['name']} ({template['id']})")

        # Get a specific template
        fullstack_template = template_manager.get_template('greenfield-fullstack')
        if fullstack_template:
            print(f"\nTemplate: {fullstack_template.name}")
            print(f"Steps: {len(fullstack_template.steps)}")
            print(f"Estimated duration: {fullstack_template.total_duration_minutes} minutes")

            # Execute the workflow (mock execution)
            try:
                execution = await execution_engine.execute_workflow(
                    fullstack_template,
                    variables={"project_name": "Test Project"}
                )
                print(f"\nExecution completed with status: {execution.status}")
                print(f"Execution time: {execution.execution_time:.2f}s")

            except Exception as e:
                print(f"Execution failed: {e}")

    # Run the example
    asyncio.run(main())

    def _create_greenfield_fullstack_template(self) -> WorkflowDefinition:
        """Create greenfield fullstack development template"""
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Requirements Analysis",
                type=StepType.TASK,
                description="Analyze requirements and create project brief",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "analyst"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "documentation"],
                    preferred_agents=["analyst"]
                ),
                estimated_duration=1800.0,  # 30 minutes
                tags={"analysis", "documentation"}
            ),
            "prd_creation": WorkflowStep(
                id="prd_creation",
                name="Product Requirements Document",
                type=StepType.TASK,
                description="Create comprehensive PRD from project brief",
                command="create_document",
                parameters={
                    "template": "prd-tmpl",
                    "output": "docs/prd.md",
                    "agent_role": "pm",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["product_management", "documentation"],
                    preferred_agents=["pm"]
                ),
                estimated_duration=2400.0,  # 40 minutes
                tags={"product_management", "documentation"}
            ),
            "ux_specification": WorkflowStep(
                id="ux_specification",
                name="UX/UI Specification",
                type=StepType.TASK,
                description="Create frontend specification and user experience design",
                command="create_document",
                parameters={
                    "template": "front-end-spec-tmpl",
                    "output": "docs/front-end-spec.md",
                    "agent_role": "ux-expert",
                    "inputs": ["docs/prd.md"]
                },
                depends_on=["prd_creation"],
                agent_requirements=AgentRequirement(
                    capabilities=["ux_design", "frontend", "documentation"],
                    preferred_agents=["ux-expert"]
                ),
                estimated_duration=3000.0,  # 50 minutes
                tags={"ux_design", "frontend", "documentation"}
            ),
            "architecture_design": WorkflowStep(
                id="architecture_design",
                name="System Architecture",
                type=StepType.TASK,
                description="Design system architecture and technical specifications",
                command="create_document",
                parameters={
                    "template": "fullstack-architecture-tmpl",
                    "output": "docs/architecture.md",
                    "agent_role": "architect",
                    "inputs": ["docs/prd.md", "docs/front-end-spec.md"]
                },
                depends_on=["prd_creation", "ux_specification"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "system_design", "documentation"],
                    preferred_agents=["architect"]
                ),
                estimated_duration=3600.0,  # 60 minutes
                tags={"architecture", "system_design", "documentation"}
            ),
            "validation": WorkflowStep(
                id="validation",
                name="Product Owner Validation",
                type=StepType.CONDITION,
                description="Validate all artifacts with product owner checklist",
                command="validate_documents",
                parameters={
                    "checklist": "po-master-checklist",
                    "documents": ["docs/project-brief.md", "docs/prd.md", "docs/front-end-spec.md", "docs/architecture.md"],
                    "agent_role": "po"
                },
                depends_on=["architecture_design"],
                agent_requirements=AgentRequirement(
                    capabilities=["product_ownership", "validation"],
                    preferred_agents=["po"]
                ),
                condition=StepCondition(
                    variable="validation_required",
                    operator=ConditionOperator.EQUALS,
                    value=True
                ),
                estimated_duration=1200.0,  # 20 minutes
                tags={"validation", "quality_assurance"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete workflow for creating a new fullstack application from scratch",
            author="BMAD Method",
            tags={"greenfield", "fullstack", "web", "complete"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=7200.0,  # 2 hours
            parallel_execution=False,
            max_concurrent_steps=2,
            required_capabilities=["analysis", "architecture", "backend", "frontend"],
            pheromone_integration=True,
            progress_reporting=True,
            project_types=["fullstack", "web", "greenfield"]
        )

    def _create_greenfield_service_template(self) -> WorkflowDefinition:
        """Create greenfield service development template"""
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Service Requirements Analysis",
                type=StepType.TASK,
                description="Analyze service requirements and create project brief",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "analyst",
                    "focus": "backend_service"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "backend", "documentation"],
                    preferred_agents=["analyst"]
                ),
                estimated_duration=1200.0,  # 20 minutes
                tags={"analysis", "backend", "service"}
            ),
            "api_design": WorkflowStep(
                id="api_design",
                name="API Design",
                type=StepType.TASK,
                description="Design API endpoints and data models",
                command="create_document",
                parameters={
                    "template": "api-spec-tmpl",
                    "output": "docs/api-spec.md",
                    "agent_role": "architect",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["api_design", "backend", "documentation"],
                    preferred_agents=["architect"]
                ),
                estimated_duration=2400.0,  # 40 minutes
                tags={"api_design", "backend", "architecture"}
            ),
            "implementation": WorkflowStep(
                id="implementation",
                name="Service Implementation",
                type=StepType.TASK,
                description="Implement the backend service",
                command="implement_service",
                parameters={
                    "framework": "fastapi",
                    "database": "postgresql",
                    "agent_role": "developer",
                    "inputs": ["docs/api-spec.md"]
                },
                depends_on=["api_design"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend_development", "python", "api"],
                    preferred_agents=["developer"]
                ),
                estimated_duration=5400.0,  # 90 minutes
                tags={"implementation", "backend", "development"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-service",
            name="Greenfield Service Development",
            version="1.0.0",
            description="Workflow for creating a new backend service from scratch",
            author="BMAD Method",
            tags={"greenfield", "service", "backend", "api"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=5400.0,  # 90 minutes
            parallel_execution=False,
            required_capabilities=["analysis", "backend", "api"],
            project_types=["service", "backend", "api", "greenfield"]
        )

    def _create_greenfield_ui_template(self) -> WorkflowDefinition:
        """Create greenfield UI development template"""
        steps = {
            "ux_analysis": WorkflowStep(
                id="ux_analysis",
                name="UX Requirements Analysis",
                type=StepType.TASK,
                description="Analyze UI/UX requirements",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "ux-expert",
                    "focus": "frontend_ui"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["ux_design", "frontend", "analysis"],
                    preferred_agents=["ux-expert"]
                ),
                estimated_duration=1800.0,  # 30 minutes
                tags={"ux_design", "frontend", "analysis"}
            ),
            "ui_implementation": WorkflowStep(
                id="ui_implementation",
                name="UI Implementation",
                type=StepType.TASK,
                description="Implement the user interface",
                command="implement_ui",
                parameters={
                    "framework": "react",
                    "styling": "tailwind",
                    "agent_role": "frontend-dev",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["ux_analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend_development", "react", "ui"],
                    preferred_agents=["frontend-dev"]
                ),
                estimated_duration=4800.0,  # 80 minutes
                tags={"implementation", "frontend", "ui"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-ui",
            name="Greenfield UI Development",
            version="1.0.0",
            description="Workflow for creating a new user interface from scratch",
            author="BMAD Method",
            tags={"greenfield", "ui", "frontend", "react"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600.0,  # 60 minutes
            parallel_execution=False,
            required_capabilities=["ux_design", "frontend"],
            project_types=["ui", "frontend", "greenfield"]
        )

    def _create_brownfield_fullstack_template(self) -> WorkflowDefinition:
        """Create brownfield fullstack enhancement template"""
        # Simplified brownfield template - would be expanded in full implementation
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Existing System Analysis",
                type=StepType.TASK,
                description="Analyze existing system and enhancement requirements",
                command="analyze_existing_system",
                parameters={"agent_role": "architect"},
                estimated_duration=2400.0,
                tags={"analysis", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-fullstack",
            name="Brownfield Fullstack Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing fullstack applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "fullstack", "enhancement"]
        )

    def _create_brownfield_service_template(self) -> WorkflowDefinition:
        """Create brownfield service enhancement template"""
        steps = {
            "service_analysis": WorkflowStep(
                id="service_analysis",
                name="Service Analysis",
                type=StepType.TASK,
                description="Analyze existing service for enhancements",
                command="analyze_service",
                parameters={"agent_role": "architect"},
                estimated_duration=1800.0,
                tags={"analysis", "service", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-service",
            name="Brownfield Service Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing backend services",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "service", "enhancement"]
        )

    def _create_brownfield_ui_template(self) -> WorkflowDefinition:
        """Create brownfield UI enhancement template"""
        steps = {
            "ui_analysis": WorkflowStep(
                id="ui_analysis",
                name="UI Analysis",
                type=StepType.TASK,
                description="Analyze existing UI for improvements",
                command="analyze_ui",
                parameters={"agent_role": "ux-expert"},
                estimated_duration=1500.0,
                tags={"analysis", "ui", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-ui",
            name="Brownfield UI Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing user interfaces",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "ui", "enhancement"]
        )

    def _create_game_prototype_template(self) -> WorkflowDefinition:
        """Create game prototype development template"""
        steps = {
            "concept": WorkflowStep(
                id="concept",
                name="Game Concept",
                type=StepType.TASK,
                description="Define game concept and mechanics",
                command="create_game_concept",
                parameters={"agent_role": "game-designer"},
                estimated_duration=1800.0,
                tags={"concept", "game", "design"}
            )
        }

        return WorkflowDefinition(
            id="game-prototype",
            name="Game Prototype Development",
            version="1.0.0",
            description="Rapid game prototyping workflow",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["game", "prototype", "gaming"]
        )

    def _create_api_service_template(self) -> WorkflowDefinition:
        """Create API service development template"""
        steps = {
            "api_design": WorkflowStep(
                id="api_design",
                name="API Design",
                type=StepType.TASK,
                description="Design RESTful API endpoints",
                command="design_api",
                parameters={"agent_role": "architect"},
                estimated_duration=2400.0,
                tags={"api", "design", "backend"}
            )
        }

        return WorkflowDefinition(
            id="api-service",
            name="API Service Development",
            version="1.0.0",
            description="Workflow for creating RESTful API services",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["api", "service", "backend"]
        )

    def _create_mobile_app_template(self) -> WorkflowDefinition:
        """Create mobile app development template"""
        steps = {
            "mobile_design": WorkflowStep(
                id="mobile_design",
                name="Mobile App Design",
                type=StepType.TASK,
                description="Design mobile application interface and flow",
                command="design_mobile_app",
                parameters={"agent_role": "mobile-designer"},
                estimated_duration=3600.0,
                tags={"mobile", "design", "app"}
            )
        }

        return WorkflowDefinition(
            id="mobile-app",
            name="Mobile App Development",
            version="1.0.0",
            description="Workflow for creating mobile applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["mobile", "app", "ios", "android"]
        )

    def _create_desktop_app_template(self) -> WorkflowDefinition:
        """Create desktop app development template"""
        steps = {
            "desktop_design": WorkflowStep(
                id="desktop_design",
                name="Desktop App Design",
                type=StepType.TASK,
                description="Design desktop application interface",
                command="design_desktop_app",
                parameters={"agent_role": "desktop-designer"},
                estimated_duration=3000.0,
                tags={"desktop", "design", "app"}
            )
        }

        return WorkflowDefinition(
            id="desktop-app",
            name="Desktop App Development",
            version="1.0.0",
            description="Workflow for creating desktop applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["desktop", "app", "electron"]
        )


class WorkflowExecutionEngine:
    """Enhanced workflow execution engine with comprehensive features"""

    def __init__(self, pheromone_bus=None):
        self.pheromone_bus = pheromone_bus
        self.active_executions: Dict[str, WorkflowExecution] = {}
        self.agent_registry: Dict[str, Dict[str, Any]] = {}
        self.execution_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.template_manager = WorkflowTemplateManager()
        self.monitor = WorkflowMonitor(pheromone_bus)

        # Performance tracking
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0
        }

    def get_available_templates(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available workflow templates"""
        return self.template_manager.list_templates(category)

    def search_templates(self, query: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for workflow templates"""
        return self.template_manager.search_templates(query, project_type)

    def get_template_by_id(self, template_id: str) -> Optional[WorkflowDefinition]:
        """Get a specific workflow template"""
        return self.template_manager.get_template(template_id)

    def recommend_template(self, project_description: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Recommend workflow templates based on project description"""
        # Simple keyword-based recommendation
        keywords = project_description.lower().split()

        # Score templates based on keyword matches
        templates = self.get_available_templates()
        scored_templates = []

        for template in templates:
            score = 0

            # Check name matches
            for keyword in keywords:
                if keyword in template['name'].lower():
                    score += 3
                if keyword in template['description'].lower():
                    score += 2
                if keyword in [tag.lower() for tag in template['tags']]:
                    score += 1

            # Boost score for project type match
            if project_type and project_type in template['project_types']:
                score += 5

            if score > 0:
                template_copy = template.copy()
                template_copy['recommendation_score'] = score
                scored_templates.append(template_copy)

        # Sort by score (highest first)
        scored_templates.sort(key=lambda t: t['recommendation_score'], reverse=True)

        return scored_templates[:5]  # Return top 5 recommendations

    def create_custom_template(self, template_data: Dict[str, Any]) -> str:
        """Create a custom workflow template"""
        template_id = template_data.get('id', f"custom_{uuid.uuid4().hex[:8]}")

        # Parse template data into WorkflowDefinition
        parser = WorkflowYAMLParser()
        workflow = parser.parse_yaml_data(template_data)

        # Add to template manager
        self.template_manager.add_custom_template(template_id, workflow)

        return template_id

    def delete_custom_template(self, template_id: str) -> bool:
        """Delete a custom workflow template"""
        return self.template_manager.remove_custom_template(template_id)

    def get_execution_monitoring_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get real-time monitoring data for an execution"""
        return self.monitor.get_monitoring_data(execution_id)

    def subscribe_to_monitoring_updates(self, callback: Callable):
        """Subscribe to real-time monitoring updates"""
        self.monitor.subscribe_to_updates(callback)

    def unsubscribe_from_monitoring_updates(self, callback: Callable):
        """Unsubscribe from monitoring updates"""
        self.monitor.unsubscribe_from_updates(callback)

    def get_all_active_executions(self) -> List[Dict[str, Any]]:
        """Get status of all active executions"""
        active_executions = []

        for execution_id, execution in self.active_executions.items():
            monitoring_data = self.get_execution_monitoring_data(execution_id)
            if monitoring_data:
                active_executions.append(monitoring_data)

        return active_executions

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get comprehensive execution statistics"""
        with self.execution_lock:
            stats = self.execution_stats.copy()

        # Add current active execution count
        stats["active_executions"] = len(self.active_executions)

        # Add monitoring statistics
        stats["active_monitors"] = len(self.monitor.active_monitors)

        return stats

    def generate_workflow_diagram(self, workflow: WorkflowDefinition, execution: Optional[WorkflowExecution] = None) -> str:
        """Generate Mermaid diagram for workflow visualization"""
        mermaid_lines = ["graph TD"]

        # Add nodes for each step
        for step_id, step in workflow.steps.items():
            # Determine node style based on step type and status
            node_style = self._get_node_style(step, execution)
            node_label = f"{step.name}"

            # Add status indicator if execution is provided
            if execution and step_id in execution.step_status:
                status = execution.step_status[step_id]
                status_icon = self._get_status_icon(status)
                node_label = f"{status_icon} {node_label}"

            mermaid_lines.append(f'    {step_id}["{node_label}"]{node_style}')

        # Add edges for dependencies
        for step_id, step in workflow.steps.items():
            for dep_id in step.depends_on:
                if dep_id in workflow.steps:
                    mermaid_lines.append(f"    {dep_id} --> {step_id}")

        # Add parallel execution indicators
        for step_id, step in workflow.steps.items():
            if step.parallel_steps:
                for parallel_step in step.parallel_steps:
                    if parallel_step in workflow.steps:
                        mermaid_lines.append(f"    {step_id} -.-> {parallel_step}")

        # Add conditional paths
        for step_id, step in workflow.steps.items():
            if step.condition:
                condition_node = f"{step_id}_condition"
                mermaid_lines.append(f'    {condition_node}{{"{step.condition.description or "Condition"}"}}')
                mermaid_lines.append(f"    {condition_node} -->|Yes| {step_id}")

                # Add dependencies to condition node
                for dep_id in step.depends_on:
                    if dep_id in workflow.steps:
                        mermaid_lines.append(f"    {dep_id} --> {condition_node}")

        # Add styling
        mermaid_lines.extend([
            "",
            "    classDef pending fill:#f9f9f9,stroke:#333,stroke-width:2px",
            "    classDef running fill:#fff3cd,stroke:#856404,stroke-width:3px",
            "    classDef completed fill:#d4edda,stroke:#155724,stroke-width:2px",
            "    classDef failed fill:#f8d7da,stroke:#721c24,stroke-width:2px",
            "    classDef skipped fill:#e2e3e5,stroke:#6c757d,stroke-width:1px",
            "    classDef condition fill:#e7f3ff,stroke:#0066cc,stroke-width:2px"
        ])

        return "\n".join(mermaid_lines)

    def _get_node_style(self, step: WorkflowStep, execution: Optional[WorkflowExecution]) -> str:
        """Get Mermaid node style based on step type and status"""
        if not execution:
            return ":::pending"

        status = execution.step_status.get(step.id, StepStatus.PENDING)

        style_map = {
            StepStatus.PENDING: ":::pending",
            StepStatus.RUNNING: ":::running",
            StepStatus.COMPLETED: ":::completed",
            StepStatus.FAILED: ":::failed",
            StepStatus.SKIPPED: ":::skipped",
            StepStatus.OPTIONAL_FAILED: ":::skipped"
        }

        return style_map.get(status, ":::pending")

    def _get_status_icon(self, status: StepStatus) -> str:
        """Get status icon for visualization"""
        icon_map = {
            StepStatus.PENDING: "⏳",
            StepStatus.RUNNING: "🔄",
            StepStatus.COMPLETED: "✅",
            StepStatus.FAILED: "❌",
            StepStatus.SKIPPED: "⏭️",
            StepStatus.OPTIONAL_FAILED: "⚠️",
            StepStatus.RETRYING: "🔁"
        }

        return icon_map.get(status, "⏳")

    def get_workflow_progress(self, execution: WorkflowExecution) -> Dict[str, Any]:
        """Get detailed workflow progress information"""
        total_steps = len(execution.step_status)
        if total_steps == 0:
            return {
                "overall_progress": 0.0,
                "completed_steps": 0,
                "total_steps": 0,
                "failed_steps": execution.failed_steps,
                "skipped_steps": execution.skipped_steps,
                "step_details": [],
                "estimated_completion": None,
                "elapsed_time": time.time() - execution.started_at if execution.started_at else 0
            }

        # Calculate overall progress
        completed_steps = sum(1 for status in execution.step_status.values()
                            if status in [StepStatus.COMPLETED, StepStatus.SKIPPED])
        overall_progress = (completed_steps / total_steps) * 100

        # Get step details with progress
        step_details = []
        for step_id, status in execution.step_status.items():
            step_info = {
                "id": step_id,
                "name": step_id.replace("_", " ").title(),
                "status": status.value,
                "progress": 100 if status in [StepStatus.COMPLETED, StepStatus.SKIPPED] else
                          50 if status == StepStatus.RUNNING else 0,
                "start_time": execution.step_start_times.get(step_id),
                "end_time": execution.step_end_times.get(step_id),
                "duration": self._calculate_step_duration(execution, step_id),
                "result": execution.step_results.get(step_id, {})
            }
            step_details.append(step_info)

        # Calculate estimated completion time
        estimated_completion = self._estimate_completion_time(execution)

        return {
            "overall_progress": overall_progress,
            "completed_steps": completed_steps,
            "total_steps": total_steps,
            "failed_steps": execution.failed_steps,
            "skipped_steps": execution.skipped_steps,
            "step_details": step_details,
            "estimated_completion": estimated_completion,
            "elapsed_time": time.time() - execution.started_at if execution.started_at else 0
        }

    def _calculate_step_duration(self, execution: WorkflowExecution, step_id: str) -> Optional[float]:
        """Calculate duration of a step"""
        start_time = execution.step_start_times.get(step_id)
        end_time = execution.step_end_times.get(step_id)

        if start_time and end_time:
            return end_time - start_time
        elif start_time:
            return time.time() - start_time

        return None

    def _estimate_completion_time(self, execution: WorkflowExecution) -> Optional[float]:
        """Estimate workflow completion time"""
        if not execution.started_at:
            return None

        elapsed_time = time.time() - execution.started_at
        total_steps = len(execution.step_status)
        completed_steps = sum(1 for status in execution.step_status.values()
                            if status in [StepStatus.COMPLETED, StepStatus.SKIPPED])

        if completed_steps == 0:
            return None

        # Simple linear estimation based on current progress
        progress_ratio = completed_steps / total_steps
        estimated_total_time = elapsed_time / progress_ratio
        estimated_remaining = estimated_total_time - elapsed_time

        return max(0, estimated_remaining)


@dataclass
class ParallelExecutionState:
    """State management for parallel workflow execution"""
    pending_steps: Set[str]
    running_tasks: Dict[str, asyncio.Task]
    completed_steps: Set[str]
    failed_steps: Set[str]
    max_concurrent: int
    resource_manager: 'ResourceManager'


class ResourceManager:
    """Manages computational resources for parallel execution"""

    def __init__(self):
        self.cpu_usage = 0.0
        self.memory_usage = 0.0
        self.allocated_resources: Dict[str, Dict[str, float]] = {}
        self.max_cpu_usage = 0.8  # 80% max CPU usage
        self.max_memory_usage = 0.8  # 80% max memory usage

    def can_allocate_resources(self, step: WorkflowStep) -> bool:
        """Check if resources can be allocated for a step"""
        # Estimate resource requirements based on step type and parameters
        estimated_cpu = self._estimate_cpu_requirement(step)
        estimated_memory = self._estimate_memory_requirement(step)

        return (self.cpu_usage + estimated_cpu <= self.max_cpu_usage and
                self.memory_usage + estimated_memory <= self.max_memory_usage)

    def allocate_resources(self, step: WorkflowStep):
        """Allocate resources for a step"""
        cpu_req = self._estimate_cpu_requirement(step)
        memory_req = self._estimate_memory_requirement(step)

        self.allocated_resources[step.id] = {
            'cpu': cpu_req,
            'memory': memory_req
        }

        self.cpu_usage += cpu_req
        self.memory_usage += memory_req

    def free_resources(self, step_id: str):
        """Free resources allocated to a step"""
        if step_id in self.allocated_resources:
            resources = self.allocated_resources.pop(step_id)
            self.cpu_usage -= resources['cpu']
            self.memory_usage -= resources['memory']

            # Ensure usage doesn't go negative due to floating point errors
            self.cpu_usage = max(0.0, self.cpu_usage)
            self.memory_usage = max(0.0, self.memory_usage)

    def update_availability(self):
        """Update resource availability based on system monitoring"""
        try:
            import psutil

            # Validate current state
            if not hasattr(self, 'cpu_usage'):
                self.cpu_usage = 0.1  # Default low usage
            if not hasattr(self, 'memory_usage'):
                self.memory_usage = 0.1  # Default low usage

            # Update CPU availability with error handling
            try:
                cpu_percent = psutil.cpu_percent(interval=0.1)
                if cpu_percent is not None and 0 <= cpu_percent <= 100:
                    self.cpu_usage = min(1.0, max(0.0, cpu_percent / 100.0))
                else:
                    logger.warning(f"Invalid CPU percentage: {cpu_percent}")
            except (psutil.Error, OSError) as e:
                logger.warning(f"Failed to get CPU usage: {e}")

            # Update memory availability with error handling
            try:
                memory = psutil.virtual_memory()
                if memory and hasattr(memory, 'percent') and 0 <= memory.percent <= 100:
                    self.memory_usage = min(1.0, max(0.0, memory.percent / 100.0))
                else:
                    logger.warning(f"Invalid memory data: {memory}")
            except (psutil.Error, OSError) as e:
                logger.warning(f"Failed to get memory usage: {e}")

            # Update disk availability (check main disk) with error handling
            try:
                import os
                disk_path = '/' if os.name != 'nt' else 'C:\\'
                disk = psutil.disk_usage(disk_path)
                if disk and disk.total > 0:
                    disk_usage = (disk.used / disk.total)
                    if 0 <= disk_usage <= 1:
                        # Store disk usage for potential future use
                        self.disk_usage = disk_usage
                    else:
                        logger.warning(f"Invalid disk usage calculation: {disk_usage}")
                else:
                    logger.warning(f"Invalid disk data: {disk}")
            except (psutil.Error, OSError) as e:
                logger.warning(f"Failed to get disk usage: {e}")

            # Ensure values are within valid range
            self.cpu_usage = max(0.0, min(1.0, self.cpu_usage))
            self.memory_usage = max(0.0, min(1.0, self.memory_usage))

            logger.debug(f"Resource usage updated - CPU: {self.cpu_usage:.2f}, Memory: {self.memory_usage:.2f}")

        except ImportError:
            # Fallback to simulated resource monitoring
            logger.info("psutil not available, using simulated resource monitoring")
            self._simulate_resource_monitoring()

        except Exception as e:
            logger.error(f"Unexpected error updating resource availability: {e}")
            # Ensure we have valid fallback values
            if not hasattr(self, 'cpu_usage') or self.cpu_usage < 0 or self.cpu_usage > 1:
                self.cpu_usage = 0.2  # Conservative fallback
            if not hasattr(self, 'memory_usage') or self.memory_usage < 0 or self.memory_usage > 1:
                self.memory_usage = 0.2  # Conservative fallback

    def _simulate_resource_monitoring(self):
        """Simulate resource monitoring when psutil is not available"""
        import random

        # Initialize if not present
        if not hasattr(self, 'cpu_usage'):
            self.cpu_usage = 0.1
        if not hasattr(self, 'memory_usage'):
            self.memory_usage = 0.1

        # Simulate some resource usage variation with bounds checking
        cpu_change = random.uniform(-0.1, 0.1)
        memory_change = random.uniform(-0.05, 0.05)

        self.cpu_usage = max(0.0, min(1.0, self.cpu_usage + cpu_change))
        self.memory_usage = max(0.0, min(1.0, self.memory_usage + memory_change))

        logger.debug(f"Simulated resource usage - CPU: {self.cpu_usage:.2f}, Memory: {self.memory_usage:.2f}")

    def _estimate_cpu_requirement(self, step: WorkflowStep) -> float:
        """Estimate CPU requirement for a step"""
        # Base CPU requirement
        base_cpu = 0.1

        # Adjust based on step type
        if step.type == StepType.PARALLEL:
            base_cpu *= 1.5
        elif step.type == StepType.LOOP:
            base_cpu *= 2.0

        # Adjust based on estimated duration (longer tasks need more CPU)
        if step.estimated_duration > 3600:  # > 1 hour
            base_cpu *= 1.5

        return min(base_cpu, 0.3)  # Cap at 30% CPU per step

    def _estimate_memory_requirement(self, step: WorkflowStep) -> float:
        """Estimate memory requirement for a step"""
        # Base memory requirement
        base_memory = 0.05  # 5% of system memory

        # Adjust based on step parameters
        if 'large_dataset' in step.parameters:
            base_memory *= 3.0
        elif 'memory_intensive' in step.parameters:
            base_memory *= 2.0

        return min(base_memory, 0.2)  # Cap at 20% memory per step

    async def _execute_step_with_cleanup(self, step: WorkflowStep, execution: WorkflowExecution, workflow: WorkflowDefinition, state: ParallelExecutionState) -> bool:
        """Execute a step with automatic resource cleanup"""
        try:
            result = await self._execute_step(step, execution, workflow)
            return result
        finally:
            # Always free resources when step completes
            state.resource_manager.free_resources(step.id)

    async def _detect_deadlock(self, state: ParallelExecutionState, workflow: WorkflowDefinition) -> bool:
        """Detect potential deadlock in parallel execution"""
        if not state.pending_steps or state.running_tasks:
            return False

        # Check if any pending step can ever be satisfied
        for step_id in state.pending_steps:
            step = workflow.steps[step_id]
            can_be_satisfied = True

            for dep_id in step.depends_on:
                if dep_id in state.failed_steps:
                    # Dependency failed and step is not optional
                    if not step.optional:
                        can_be_satisfied = False
                        break
                elif dep_id in state.pending_steps:
                    # Circular dependency check
                    if self._has_circular_dependency(step_id, dep_id, workflow, state.pending_steps):
                        can_be_satisfied = False
                        break

            if can_be_satisfied:
                return False

        return True  # All pending steps are blocked

    def _has_circular_dependency(self, step_id: str, dep_id: str, workflow: WorkflowDefinition, pending_steps: Set[str], visited: Optional[Set[str]] = None) -> bool:
        """Check for circular dependencies"""
        if visited is None:
            visited = set()

        if dep_id == step_id:
            return True

        if dep_id in visited or dep_id not in pending_steps:
            return False

        visited.add(dep_id)
        dep_step = workflow.steps.get(dep_id)

        if dep_step:
            for transitive_dep in dep_step.depends_on:
                if self._has_circular_dependency(step_id, transitive_dep, workflow, pending_steps, visited):
                    return True

        visited.remove(dep_id)
        return False

    async def _resolve_deadlock(self, state: ParallelExecutionState, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Attempt to resolve deadlock by skipping optional steps or failing gracefully"""
        logger.warning("Attempting deadlock resolution")

        # Find optional steps that can be skipped
        skippable_steps = []
        for step_id in state.pending_steps:
            step = workflow.steps[step_id]
            if step.optional:
                skippable_steps.append(step_id)

        # Skip optional steps to break deadlock
        for step_id in skippable_steps:
            logger.info(f"Skipping optional step {step_id} to resolve deadlock")
            state.pending_steps.remove(step_id)
            execution.step_status[step_id] = StepStatus.SKIPPED
            execution.skipped_steps += 1
            state.completed_steps.add(step_id)

        # If no optional steps to skip, mark remaining as failed
        if not skippable_steps and state.pending_steps:
            logger.error("Cannot resolve deadlock - marking remaining steps as failed")
            for step_id in list(state.pending_steps):
                state.pending_steps.remove(step_id)
                execution.step_status[step_id] = StepStatus.FAILED
                execution.failed_steps += 1
                state.failed_steps.add(step_id)

    async def _cleanup_remaining_tasks(self, state: ParallelExecutionState, execution: WorkflowExecution):
        """Clean up any remaining running tasks"""
        if state.running_tasks:
            logger.info(f"Waiting for {len(state.running_tasks)} remaining tasks to complete")

            try:
                # Wait for all remaining tasks with a reasonable timeout
                await asyncio.wait_for(
                    asyncio.gather(*state.running_tasks.values(), return_exceptions=True),
                    timeout=30.0  # 30 second timeout for cleanup
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within cleanup timeout")

                # Cancel remaining tasks
                for step_id, task in state.running_tasks.items():
                    if not task.done():
                        task.cancel()
                        execution.step_status[step_id] = StepStatus.FAILED
                        execution.failed_steps += 1

            # Free all remaining resources
            for step_id in state.running_tasks:
                state.resource_manager.free_resources(step_id)


class WorkflowMonitor:
    """Real-time workflow monitoring and status tracking"""

    def __init__(self, pheromone_bus=None):
        self.pheromone_bus = pheromone_bus
        self.active_monitors: Dict[str, 'ExecutionMonitor'] = {}
        self.monitoring_enabled = True
        self.update_interval = 1.0  # seconds
        self.subscribers: List[Callable] = []

    def start_monitoring(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> str:
        """Start monitoring a workflow execution"""
        monitor_id = f"monitor_{execution.id}"

        monitor = ExecutionMonitor(
            execution_id=execution.id,
            workflow_id=workflow.id,
            workflow_name=workflow.name,
            total_steps=len(workflow.steps),
            pheromone_bus=self.pheromone_bus
        )

        self.active_monitors[monitor_id] = monitor
        monitor.start()

        logger.info(f"Started monitoring for execution {execution.id}")
        return monitor_id

    def stop_monitoring(self, monitor_id: str):
        """Stop monitoring a workflow execution"""
        if monitor_id in self.active_monitors:
            monitor = self.active_monitors.pop(monitor_id)
            monitor.stop()
            logger.info(f"Stopped monitoring {monitor_id}")

    def update_execution_status(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Update execution status for all relevant monitors"""
        for monitor in self.active_monitors.values():
            if monitor.execution_id == execution.id:
                monitor.update_status(execution, workflow)

    def get_monitoring_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get current monitoring data for an execution"""
        for monitor in self.active_monitors.values():
            if monitor.execution_id == execution_id:
                return monitor.get_current_status()
        return None

    def subscribe_to_updates(self, callback: Callable):
        """Subscribe to real-time monitoring updates"""
        self.subscribers.append(callback)

    def unsubscribe_from_updates(self, callback: Callable):
        """Unsubscribe from monitoring updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def broadcast_update(self, update_data: Dict[str, Any]):
        """Broadcast update to all subscribers"""
        for callback in self.subscribers:
            try:
                callback(update_data)
            except Exception as e:
                logger.error(f"Error broadcasting update: {e}")


@dataclass
class ExecutionMonitor:
    """Individual execution monitor with real-time tracking"""
    execution_id: str
    workflow_id: str
    workflow_name: str
    total_steps: int
    pheromone_bus: Any = None

    # Monitoring state
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    update_count: int = 0
    is_active: bool = True

    # Progress tracking
    completed_steps: int = 0
    failed_steps: int = 0
    skipped_steps: int = 0
    running_steps: int = 0

    # Performance metrics
    step_durations: Dict[str, float] = field(default_factory=dict)
    step_start_times: Dict[str, float] = field(default_factory=dict)
    throughput_history: List[float] = field(default_factory=list)

    def start(self):
        """Start monitoring"""
        self.is_active = True
        self.start_time = time.time()
        logger.debug(f"Started monitoring execution {self.execution_id}")

    def stop(self):
        """Stop monitoring"""
        self.is_active = False
        logger.debug(f"Stopped monitoring execution {self.execution_id}")

    def update_status(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Update monitoring status with current execution state"""
        if not self.is_active:
            return

        current_time = time.time()
        self.last_update = current_time
        self.update_count += 1

        # Update step counts
        self.completed_steps = sum(1 for status in execution.step_status.values()
                                 if status == StepStatus.COMPLETED)
        self.failed_steps = sum(1 for status in execution.step_status.values()
                              if status == StepStatus.FAILED)
        self.skipped_steps = sum(1 for status in execution.step_status.values()
                               if status == StepStatus.SKIPPED)
        self.running_steps = sum(1 for status in execution.step_status.values()
                               if status == StepStatus.RUNNING)

        # Update step durations
        for step_id, start_time in execution.step_start_times.items():
            if step_id in execution.step_end_times:
                end_time = execution.step_end_times[step_id]
                self.step_durations[step_id] = end_time - start_time
            elif step_id not in self.step_start_times:
                self.step_start_times[step_id] = start_time

        # Calculate throughput (steps per minute)
        elapsed_time = current_time - self.start_time
        if elapsed_time > 0:
            throughput = (self.completed_steps / elapsed_time) * 60  # steps per minute
            self.throughput_history.append(throughput)

            # Keep only last 10 measurements
            if len(self.throughput_history) > 10:
                self.throughput_history.pop(0)

        # Send pheromone update if available
        if self.pheromone_bus:
            self._send_pheromone_update(execution, workflow)

    def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        elapsed_time = time.time() - self.start_time
        progress_percentage = (self.completed_steps / self.total_steps * 100) if self.total_steps > 0 else 0

        # Calculate average throughput
        avg_throughput = sum(self.throughput_history) / len(self.throughput_history) if self.throughput_history else 0

        # Estimate completion time
        remaining_steps = self.total_steps - self.completed_steps - self.failed_steps - self.skipped_steps
        estimated_completion = None
        if avg_throughput > 0 and remaining_steps > 0:
            estimated_completion = (remaining_steps / avg_throughput) * 60  # seconds

        return {
            "execution_id": self.execution_id,
            "workflow_id": self.workflow_id,
            "workflow_name": self.workflow_name,
            "status": {
                "progress_percentage": progress_percentage,
                "completed_steps": self.completed_steps,
                "failed_steps": self.failed_steps,
                "skipped_steps": self.skipped_steps,
                "running_steps": self.running_steps,
                "total_steps": self.total_steps
            },
            "timing": {
                "elapsed_time": elapsed_time,
                "estimated_completion": estimated_completion,
                "last_update": self.last_update,
                "update_count": self.update_count
            },
            "performance": {
                "average_throughput": avg_throughput,
                "step_durations": dict(self.step_durations),
                "throughput_history": list(self.throughput_history)
            }
        }

    def _send_pheromone_update(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Send monitoring update via pheromone system"""
        try:
            update_data = {
                "type": "workflow_monitoring_update",
                "execution_id": self.execution_id,
                "workflow_id": self.workflow_id,
                "progress": self.completed_steps / self.total_steps if self.total_steps > 0 else 0,
                "status": execution.status.value,
                "timestamp": time.time()
            }

            # This would integrate with the actual pheromone system
            # self.pheromone_bus.emit("monitoring_update", update_data)

        except Exception as e:
            logger.error(f"Failed to send pheromone update: {e}")

    async def start_workflow(
        self,
        workflow_id: str,
        project_id: str,
        agent_team: Dict[str, Any],
        pheromone_bus: Dict[str, Any],
        context: Dict[str, Any]
    ) -> WorkflowExecution:
        """Start workflow execution with enhanced context"""

        # Load workflow definition
        workflow = self.get_registered_workflow(workflow_id)
        if not workflow:
            workflow = await self._load_workflow_definition(workflow_id)
        if not workflow:
            raise WorkflowExecutionError(f"Workflow '{workflow_id}' not found")

        # Create execution instance
        execution = WorkflowExecution(
            id=str(uuid.uuid4()),
            workflow_id=workflow_id,
            status=WorkflowStatus.RUNNING,
            started_at=time.time(),
            project_id=project_id,
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )

        # Initialize execution context
        await self._initialize_execution_context(execution, workflow, context)

        # Register execution
        with self.execution_lock:
            self.active_executions[execution.id] = execution
            self.execution_stats["total_executions"] += 1

        # Start monitoring
        monitor_id = self.monitor.start_monitoring(execution, workflow)
        execution.monitor_id = monitor_id

        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, workflow))

        logger.info(f"Started workflow execution {execution.id} for workflow {workflow_id}")
        return execution

    async def _load_workflow_definition(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Load workflow definition by ID"""
        # First check if it's a built-in workflow
        builtin_workflow = await self._get_builtin_workflow(workflow_id)
        if builtin_workflow:
            return builtin_workflow

        # Try to load from various sources
        workflow_paths = [
            Path(f"workflows/{workflow_id}.yaml"),
            Path(f"workflows/{workflow_id}.yml"),
            Path(f"src/workflows/{workflow_id}.yaml"),
            Path(f"config/workflows/{workflow_id}.yaml")
        ]

        parser = WorkflowYAMLParser()

        for path in workflow_paths:
            if path.exists():
                try:
                    return parser.parse_workflow_file(path)
                except Exception as e:
                    logger.warning(f"Failed to load workflow from {path}: {e}")

        return None

    def register_workflow(self, workflow: WorkflowDefinition) -> None:
        """Register a workflow definition for execution"""
        self._registered_workflows = getattr(self, '_registered_workflows', {})
        self._registered_workflows[workflow.id] = workflow

    def get_registered_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get a registered workflow definition"""
        registered = getattr(self, '_registered_workflows', {})
        return registered.get(workflow_id)

    async def _get_builtin_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get built-in workflow definition"""
        builtin_workflows = {
            "greenfield-fullstack": self._create_greenfield_fullstack_workflow(),
            "enhancement": self._create_enhancement_workflow(),
            "debugging": self._create_debugging_workflow(),
            "testing": self._create_testing_workflow()
        }

        return builtin_workflows.get(workflow_id)

    def _create_greenfield_fullstack_workflow(self) -> WorkflowDefinition:
        """Create built-in greenfield fullstack workflow"""
        steps = {
            "analyze_requirements": WorkflowStep(
                id="analyze_requirements",
                name="Analyze Requirements",
                type=StepType.TASK,
                description="Analyze project requirements and create specifications",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "requirements"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"requirements": "analysis_result"}
            ),
            "design_architecture": WorkflowStep(
                id="design_architecture",
                name="Design Architecture",
                type=StepType.TASK,
                description="Design system architecture and technology stack",
                depends_on=["analyze_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "design"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"architecture": "design_result"}
            ),
            "generate_backend": WorkflowStep(
                id="generate_backend",
                name="Generate Backend",
                type=StepType.TASK,
                description="Generate backend code and API endpoints",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "api", "database"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"backend_files": "backend_result"}
            ),
            "generate_frontend": WorkflowStep(
                id="generate_frontend",
                name="Generate Frontend",
                type=StepType.TASK,
                description="Generate frontend code and user interface",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui", "react"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"frontend_files": "frontend_result"}
            ),
            "integrate_components": WorkflowStep(
                id="integrate_components",
                name="Integrate Components",
                type=StepType.TASK,
                description="Integrate frontend and backend components",
                depends_on=["generate_backend", "generate_frontend"],
                agent_requirements=AgentRequirement(
                    capabilities=["integration", "fullstack"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"integration_result": "integration_status"}
            ),
            "generate_tests": WorkflowStep(
                id="generate_tests",
                name="Generate Tests",
                type=StepType.TASK,
                description="Generate comprehensive test suites",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "qa"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"test_files": "test_result"}
            ),
            "create_documentation": WorkflowStep(
                id="create_documentation",
                name="Create Documentation",
                type=StepType.TASK,
                description="Create project documentation and README",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["documentation", "writing"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"documentation": "docs_result"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete workflow for creating a new fullstack application from scratch",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600.0,  # 1 hour
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["analysis", "architecture", "backend", "frontend"],
            pheromone_integration=True,
            progress_reporting=True
        )

    def _create_enhancement_workflow(self) -> WorkflowDefinition:
        """Create built-in enhancement workflow"""
        steps = {
            "analyze_existing": WorkflowStep(
                id="analyze_existing",
                name="Analyze Existing Code",
                type=StepType.TASK,
                description="Analyze existing codebase and identify enhancement opportunities",
                agent_requirements=AgentRequirement(capabilities=["analysis", "code_review"]),
                estimated_duration=600.0
            ),
            "plan_enhancements": WorkflowStep(
                id="plan_enhancements",
                name="Plan Enhancements",
                type=StepType.TASK,
                description="Create enhancement plan and implementation strategy",
                depends_on=["analyze_existing"],
                agent_requirements=AgentRequirement(capabilities=["planning", "architecture"]),
                estimated_duration=300.0
            ),
            "implement_changes": WorkflowStep(
                id="implement_changes",
                name="Implement Changes",
                type=StepType.TASK,
                description="Implement the planned enhancements",
                depends_on=["plan_enhancements"],
                agent_requirements=AgentRequirement(capabilities=["development", "refactoring"]),
                estimated_duration=1200.0
            ),
            "test_changes": WorkflowStep(
                id="test_changes",
                name="Test Changes",
                type=StepType.TASK,
                description="Test the implemented changes",
                depends_on=["implement_changes"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=600.0
            )
        }

        return WorkflowDefinition(
            id="enhancement",
            name="Code Enhancement Workflow",
            version="1.0.0",
            description="Workflow for enhancing existing codebases",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=2400.0,  # 40 minutes
            required_capabilities=["analysis", "development", "testing"]
        )

    def _create_debugging_workflow(self) -> WorkflowDefinition:
        """Create built-in debugging workflow"""
        steps = {
            "identify_issue": WorkflowStep(
                id="identify_issue",
                name="Identify Issue",
                type=StepType.TASK,
                description="Identify and analyze the reported issue",
                agent_requirements=AgentRequirement(capabilities=["debugging", "analysis"]),
                estimated_duration=300.0
            ),
            "reproduce_bug": WorkflowStep(
                id="reproduce_bug",
                name="Reproduce Bug",
                type=StepType.TASK,
                description="Reproduce the bug in a controlled environment",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["debugging", "testing"]),
                estimated_duration=600.0,
                optional=True
            ),
            "fix_issue": WorkflowStep(
                id="fix_issue",
                name="Fix Issue",
                type=StepType.TASK,
                description="Implement fix for the identified issue",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["development", "debugging"]),
                estimated_duration=900.0
            ),
            "verify_fix": WorkflowStep(
                id="verify_fix",
                name="Verify Fix",
                type=StepType.TASK,
                description="Verify that the fix resolves the issue",
                depends_on=["fix_issue"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="debugging",
            name="Bug Fixing Workflow",
            version="1.0.0",
            description="Workflow for identifying and fixing bugs",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1800.0,  # 30 minutes
            required_capabilities=["debugging", "development", "testing"]
        )

    def _create_testing_workflow(self) -> WorkflowDefinition:
        """Create built-in testing workflow"""
        steps = {
            "analyze_code": WorkflowStep(
                id="analyze_code",
                name="Analyze Code for Testing",
                type=StepType.TASK,
                description="Analyze code to determine testing strategy",
                agent_requirements=AgentRequirement(capabilities=["analysis", "testing"]),
                estimated_duration=300.0
            ),
            "generate_unit_tests": WorkflowStep(
                id="generate_unit_tests",
                name="Generate Unit Tests",
                type=StepType.TASK,
                description="Generate comprehensive unit tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "unit_tests"]),
                estimated_duration=600.0
            ),
            "generate_integration_tests": WorkflowStep(
                id="generate_integration_tests",
                name="Generate Integration Tests",
                type=StepType.TASK,
                description="Generate integration tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "integration_tests"]),
                estimated_duration=600.0,
                optional=True
            ),
            "run_tests": WorkflowStep(
                id="run_tests",
                name="Run Test Suite",
                type=StepType.TASK,
                description="Execute all generated tests",
                depends_on=["generate_unit_tests"],
                agent_requirements=AgentRequirement(capabilities=["testing", "execution"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="testing",
            name="Comprehensive Testing Workflow",
            version="1.0.0",
            description="Workflow for generating and running comprehensive tests",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1500.0,  # 25 minutes
            required_capabilities=["testing", "analysis"]
        )

    async def _initialize_execution_context(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        context: Dict[str, Any]
    ) -> None:
        """Initialize execution context with variables and state"""

        # Initialize workflow variables
        for name, var in workflow.variables.items():
            execution.variables[name] = var

        # Add context variables
        for name, value in context.items():
            if name not in execution.variables:
                execution.variables[name] = WorkflowVariable(
                    name=name,
                    value=value,
                    type=type(value).__name__
                )

        # Initialize step status
        for step_id in workflow.steps:
            execution.step_status[step_id] = StepStatus.PENDING
            execution.step_attempts[step_id] = 0

        # Set total steps
        execution.total_steps = len(workflow.steps)

        logger.info(f"Initialized execution context for {execution.id}")

    async def _execute_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition, context: Dict[str, Any] = None) -> None:
        """Main workflow execution loop"""

        try:
            logger.info(f"Starting workflow execution {execution.id}")

            # Drop pheromone for workflow start
            await self._drop_pheromone("workflow_started", {
                "execution_id": execution.id,
                "workflow_id": workflow.id,
                "workflow_name": workflow.name
            }, execution.project_id)

            # Check if this is a project generation workflow
            if workflow.id in ["project_generation", "fullstack", "web_application", "api_service", "mobile_application"]:
                await self._execute_project_generation_workflow(execution, workflow, context or {})
            else:
                # Execute steps based on workflow configuration
                if workflow.parallel_execution:
                    await self._execute_parallel_workflow(execution, workflow)
                else:
                    await self._execute_sequential_workflow(execution, workflow)

            # Check final status
            if execution.failed_steps > 0:
                execution.status = WorkflowStatus.FAILED
                execution.last_error = f"Workflow failed with {execution.failed_steps} failed steps"
            else:
                execution.status = WorkflowStatus.COMPLETED

            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            # Update monitoring
            self.monitor.update_execution_status(execution, workflow)

            # Update statistics
            with self.execution_lock:
                if execution.status == WorkflowStatus.COMPLETED:
                    self.execution_stats["successful_executions"] += 1
                else:
                    self.execution_stats["failed_executions"] += 1

                # Update average execution time
                total_executions = self.execution_stats["total_executions"]
                current_avg = self.execution_stats["average_execution_time"]
                new_avg = ((current_avg * (total_executions - 1)) + execution.execution_time) / total_executions
                self.execution_stats["average_execution_time"] = new_avg

            # Drop completion pheromone
            await self._drop_pheromone("workflow_completed", {
                "execution_id": execution.id,
                "status": execution.status.value,
                "execution_time": execution.execution_time,
                "completed_steps": execution.completed_steps,
                "failed_steps": execution.failed_steps
            }, execution.project_id)

            logger.info(f"Workflow execution {execution.id} completed with status {execution.status}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = str(e)
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            logger.error(f"Workflow execution {execution.id} failed: {e}")

            # Drop error pheromone
            await self._drop_pheromone("workflow_failed", {
                "execution_id": execution.id,
                "error": str(e),
                "execution_time": execution.execution_time
            }, execution.project_id)

        finally:
            # Clean up execution
            with self.execution_lock:
                self.active_executions.pop(execution.id, None)

    async def _execute_project_generation_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition, context: Dict[str, Any]) -> None:
        """Execute project generation workflow using the ProjectGenerationPipeline"""
        try:
            # Import the project generator
            from .project_generator import ProjectGenerationPipeline
            from .project_types import ProjectType, GenerationConfig

            logger.info(f"Starting project generation workflow for execution {execution.id}")

            # Extract context information
            prompt = context.get("prompt", "Generate a software project")
            project_name = context.get("project_name", "Generated Project")
            project_type = context.get("project_type", "web_application")
            project_path = context.get("project_path", f"./projects/{execution.project_id}")

            # Create generation configuration
            config = GenerationConfig(
                project_type=ProjectType(project_type),
                include_tests=context.get("include_tests", True),
                include_docs=context.get("include_docs", True),
                include_ci_cd=context.get("include_ci_cd", True),
                include_docker=context.get("include_docker", False),
                include_deployment=context.get("include_deployment", False),
                code_quality_level=context.get("code_quality_level", "standard"),
                security_level=context.get("security_level", "standard")
            )

            # Initialize and run the project generation pipeline
            pipeline = ProjectGenerationPipeline(config)

            # Drop pheromone for project generation start
            await self._drop_pheromone("project_generation_started", {
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "project_type": project_type,
                "prompt": prompt
            }, execution.project_id)

            # Execute the project generation
            result = await pipeline.generate_project(
                prompt=prompt,
                project_name=project_name,
                project_type=project_type,
                project_path=project_path,
                workflow=workflow.id
            )

            # Update execution status based on result
            if result.get("success", False):
                execution.status = WorkflowStatus.COMPLETED
                execution.completed_steps = 8  # All phases completed
                execution.total_steps = 8

                # Drop pheromone for successful completion
                await self._drop_pheromone("project_generation_completed", {
                    "execution_id": execution.id,
                    "project_id": execution.project_id,
                    "total_files": result.get("total_files", 0),
                    "project_path": result.get("project_path", project_path)
                }, execution.project_id)

                logger.info(f"Project generation completed successfully for execution {execution.id}")
            else:
                execution.status = WorkflowStatus.FAILED
                execution.last_error = result.get("error", "Project generation failed")
                execution.failed_steps = 1

                # Drop pheromone for failure
                await self._drop_pheromone("project_generation_failed", {
                    "execution_id": execution.id,
                    "project_id": execution.project_id,
                    "error": execution.last_error
                }, execution.project_id)

                logger.error(f"Project generation failed for execution {execution.id}: {execution.last_error}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = f"Project generation workflow error: {str(e)}"
            execution.failed_steps = 1

            logger.error(f"Project generation workflow failed for execution {execution.id}: {e}")

            # Drop pheromone for workflow error
            await self._drop_pheromone("project_generation_workflow_error", {
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "error": str(e)
            }, execution.project_id)

            raise e

    async def _execute_sequential_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps sequentially"""

        for step_id in workflow.step_order:
            step = workflow.steps[step_id]

            # Check if step should be executed
            if not await self._should_execute_step(step, execution, workflow):
                execution.step_status[step_id] = StepStatus.SKIPPED
                execution.skipped_steps += 1
                continue

            # Wait for dependencies
            await self._wait_for_dependencies(step, execution)

            # Execute step
            success = await self._execute_step(step, execution, workflow)

            if success:
                execution.completed_steps += 1
            else:
                execution.failed_steps += 1

                # Check if workflow should continue
                if not step.optional and not workflow.global_retry_config:
                    break

    async def _execute_parallel_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps in parallel where possible"""

        pending_steps = set(workflow.step_order)
        running_tasks = {}
        max_concurrent = workflow.max_concurrent_steps
        max_iterations = 100  # Prevent infinite loops
        iteration = 0

        while (pending_steps or running_tasks) and iteration < max_iterations:
            iteration += 1

            # Start new tasks if we have capacity
            started_new_task = False
            while len(running_tasks) < max_concurrent and pending_steps:
                # Find a step that can be started
                ready_step = None
                for step_id in list(pending_steps):  # Create a copy to avoid modification during iteration
                    step = workflow.steps[step_id]
                    if await self._are_dependencies_satisfied(step, execution):
                        ready_step = step_id
                        break

                if ready_step:
                    step = workflow.steps[ready_step]
                    pending_steps.remove(ready_step)

                    # Check if step should be executed
                    if await self._should_execute_step(step, execution, workflow):
                        task = asyncio.create_task(self._execute_step(step, execution, workflow))
                        running_tasks[ready_step] = task
                        started_new_task = True
                    else:
                        execution.step_status[ready_step] = StepStatus.SKIPPED
                        execution.skipped_steps += 1
                        started_new_task = True
                else:
                    # No ready steps available
                    break

            # Wait for at least one task to complete if we have running tasks
            if running_tasks:
                try:
                    done, pending_tasks = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=1.0  # Add timeout to prevent hanging
                    )

                    # Process completed tasks
                    for task in done:
                        # Find which step this task belongs to
                        step_id = None
                        for sid, t in running_tasks.items():
                            if t == task:
                                step_id = sid
                                break

                        if step_id:
                            try:
                                success = await task
                                del running_tasks[step_id]

                                if success:
                                    execution.completed_steps += 1
                                else:
                                    execution.failed_steps += 1
                            except Exception as e:
                                logger.error(f"Task for step {step_id} failed: {e}")
                                del running_tasks[step_id]
                                execution.failed_steps += 1

                except asyncio.TimeoutError:
                    # Timeout waiting for tasks - continue to next iteration
                    pass
            elif not started_new_task and pending_steps:
                # No tasks running and no new tasks started - might be deadlock
                logger.warning(f"Potential deadlock detected. Pending steps: {pending_steps}")
                # Skip remaining steps to avoid infinite loop
                for step_id in pending_steps:
                    execution.step_status[step_id] = StepStatus.SKIPPED
                    execution.skipped_steps += 1
                break

        if iteration >= max_iterations:
            logger.warning("Parallel workflow execution reached maximum iterations")

        # Wait for any remaining tasks to complete
        if running_tasks:
            try:
                await asyncio.wait(running_tasks.values(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout")

    async def _should_execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Check if a step should be executed based on conditions"""

        # Check step condition
        if step.condition:
            if not await self._evaluate_condition(step.condition, execution):
                return False

        # Check dependencies
        if not await self._are_dependencies_satisfied(step, execution):
            return False

        return True

    async def _evaluate_condition(self, condition: StepCondition, execution: WorkflowExecution) -> bool:
        """Evaluate a step condition with support for complex logic"""

        try:
            result = await self._evaluate_single_condition(condition, execution)
            return not result if condition.negate else result
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

    async def _evaluate_single_condition(self, condition: StepCondition, execution: WorkflowExecution) -> bool:
        """Evaluate a single condition"""

        # Handle logical operators
        if condition.operator == ConditionOperator.AND:
            for sub_condition in condition.sub_conditions:
                if not await self._evaluate_condition(sub_condition, execution):
                    return False
            return True

        elif condition.operator == ConditionOperator.OR:
            for sub_condition in condition.sub_conditions:
                if await self._evaluate_condition(sub_condition, execution):
                    return True
            return False

        elif condition.operator == ConditionOperator.NOT:
            if condition.sub_conditions:
                return not await self._evaluate_condition(condition.sub_conditions[0], execution)
            return True

        # Handle variable-based conditions
        variable = execution.variables.get(condition.variable)

        # Special handling for existence checks
        if condition.operator == ConditionOperator.EXISTS:
            return variable is not None
        elif condition.operator == ConditionOperator.NOT_EXISTS:
            return variable is None

        # If variable doesn't exist and we're not checking existence, return False
        if variable is None:
            return False

        var_value = variable.value
        condition_value = condition.value

        # Handle empty/not empty checks
        if condition.operator == ConditionOperator.IS_EMPTY:
            return not var_value or (isinstance(var_value, (list, dict, str)) and len(var_value) == 0)
        elif condition.operator == ConditionOperator.IS_NOT_EMPTY:
            return bool(var_value) and (not isinstance(var_value, (list, dict, str)) or len(var_value) > 0)

        # Convert values for comparison
        var_str = str(var_value) if var_value is not None else ""
        condition_str = str(condition_value) if condition_value is not None else ""

        # Evaluate based on operator
        if condition.operator == ConditionOperator.EQUALS:
            return var_value == condition_value
        elif condition.operator == ConditionOperator.NOT_EQUALS:
            return var_value != condition_value
        elif condition.operator == ConditionOperator.GREATER_THAN:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a > b)
        elif condition.operator == ConditionOperator.LESS_THAN:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a < b)
        elif condition.operator == ConditionOperator.GREATER_EQUAL:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a >= b)
        elif condition.operator == ConditionOperator.LESS_EQUAL:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a <= b)
        elif condition.operator == ConditionOperator.CONTAINS:
            return condition_str in var_str
        elif condition.operator == ConditionOperator.NOT_CONTAINS:
            return condition_str not in var_str
        elif condition.operator == ConditionOperator.STARTS_WITH:
            return var_str.startswith(condition_str)
        elif condition.operator == ConditionOperator.ENDS_WITH:
            return var_str.endswith(condition_str)
        elif condition.operator == ConditionOperator.IN_LIST:
            if isinstance(condition_value, list):
                return var_value in condition_value
            return False
        elif condition.operator == ConditionOperator.NOT_IN_LIST:
            if isinstance(condition_value, list):
                return var_value not in condition_value
            return True
        elif condition.operator == ConditionOperator.REGEX_MATCH:
            import re
            try:
                return bool(re.search(condition_str, var_str))
            except re.error:
                logger.warning(f"Invalid regex pattern: {condition_str}")
                return False

        return False

    def _safe_numeric_compare(self, a: Any, b: Any, compare_func: Callable) -> bool:
        """Safely compare numeric values"""
        try:
            # Try to convert to numbers
            if isinstance(a, (int, float)) and isinstance(b, (int, float)):
                return compare_func(a, b)

            # Try string to number conversion
            try:
                num_a = float(a) if '.' in str(a) else int(a)
                num_b = float(b) if '.' in str(b) else int(b)
                return compare_func(num_a, num_b)
            except (ValueError, TypeError):
                # Fall back to string comparison
                return compare_func(str(a), str(b))
        except Exception:
            return False

    async def _are_dependencies_satisfied(self, step: WorkflowStep, execution: WorkflowExecution) -> bool:
        """Check if all step dependencies are satisfied"""

        for dep_id in step.depends_on:
            dep_status = execution.step_status.get(dep_id, StepStatus.PENDING)
            if dep_status not in [StepStatus.COMPLETED, StepStatus.SKIPPED]:
                return False

        return True

    async def _wait_for_dependencies(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Wait for step dependencies to complete"""

        while not await self._are_dependencies_satisfied(step, execution):
            await asyncio.sleep(0.1)  # Small delay to prevent busy waiting

    async def _execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a single workflow step with retry logic"""

        execution.step_status[step.id] = StepStatus.RUNNING

        # Drop pheromone for step start
        await self._drop_pheromone("step_started", {
            "step_id": step.id,
            "step_name": step.name,
            "execution_id": execution.id
        }, execution.project_id)

        max_attempts = 1
        if step.retry_config:
            max_attempts = step.retry_config.max_attempts
        elif workflow.global_retry_config:
            max_attempts = workflow.global_retry_config.max_attempts

        for attempt in range(max_attempts):
            execution.step_attempts[step.id] = attempt + 1

            try:
                # Execute based on step type
                if step.type == StepType.TASK:
                    success = await self._execute_task_step(step, execution, workflow)
                elif step.type == StepType.CONDITION:
                    success = await self._execute_condition_step(step, execution, workflow)
                elif step.type == StepType.LOOP:
                    success = await self._execute_loop_step(step, execution, workflow)
                elif step.type == StepType.PARALLEL:
                    success = await self._execute_parallel_step(step, execution, workflow)
                elif step.type == StepType.AGENT_ASSIGNMENT:
                    success = await self._execute_agent_assignment_step(step, execution, workflow)
                elif step.type == StepType.PHEROMONE_DROP:
                    success = await self._execute_pheromone_step(step, execution, workflow)
                elif step.type == StepType.WAIT:
                    success = await self._execute_wait_step(step, execution, workflow)
                else:
                    success = await self._execute_custom_step(step, execution, workflow)

                if success:
                    execution.step_status[step.id] = StepStatus.COMPLETED

                    # Handle output variables
                    await self._handle_step_outputs(step, execution)

                    # Drop success pheromone
                    await self._drop_pheromone("step_completed", {
                        "step_id": step.id,
                        "step_name": step.name,
                        "execution_id": execution.id,
                        "attempt": attempt + 1
                    }, execution.project_id)

                    return True
                else:
                    # Step failed, check if we should retry
                    if attempt < max_attempts - 1:
                        execution.step_status[step.id] = StepStatus.RETRYING

                        # Calculate retry delay
                        delay = self._calculate_retry_delay(step, workflow, attempt)
                        await asyncio.sleep(delay)

                        # Drop retry pheromone
                        await self._drop_pheromone("step_retrying", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempt": attempt + 1,
                            "next_attempt_in": delay
                        }, execution.project_id)
                    else:
                        # All attempts failed
                        if step.optional:
                            execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        else:
                            execution.step_status[step.id] = StepStatus.FAILED

                        # Drop failure pheromone
                        await self._drop_pheromone("step_failed", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempts": max_attempts,
                            "optional": step.optional
                        }, execution.project_id)

                        return step.optional  # Return True for optional steps

            except Exception as e:
                logger.error(f"Error executing step {step.id}: {e}")
                execution.last_error = str(e)

                if attempt < max_attempts - 1:
                    # Retry on exception
                    execution.step_status[step.id] = StepStatus.RETRYING
                    delay = self._calculate_retry_delay(step, workflow, attempt)
                    await asyncio.sleep(delay)
                else:
                    # Final failure
                    if step.optional:
                        execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        return True
                    else:
                        execution.step_status[step.id] = StepStatus.FAILED
                        return False

        return False

    def _calculate_retry_delay(self, step: WorkflowStep, workflow: WorkflowDefinition, attempt: int) -> float:
        """Calculate retry delay with exponential backoff"""

        retry_config = step.retry_config or workflow.global_retry_config
        if not retry_config:
            return 1.0

        delay = retry_config.delay_seconds * (retry_config.backoff_multiplier ** attempt)
        return min(delay, retry_config.max_delay_seconds)

    async def _execute_task_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a task step"""

        try:
            # Assign agent if needed
            agent_id = await self._assign_agent_to_step(step, execution)
            if not agent_id:
                logger.error(f"Failed to assign agent to step {step.id}")
                return False

            execution.agent_assignments[step.id] = agent_id

            # Prepare step context
            step_context = {
                "step_id": step.id,
                "step_name": step.name,
                "command": step.command,
                "parameters": step.parameters,
                "environment": step.environment,
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "variables": {name: var.value for name, var in execution.variables.items()}
            }

            # Execute step through agent
            start_time = time.time()
            result = await self._execute_step_with_agent(agent_id, step_context)
            end_time = time.time()

            execution.agent_response_times[step.id] = end_time - start_time
            execution.step_results[step.id] = result

            return result.get("success", False) if isinstance(result, dict) else bool(result)

        except Exception as e:
            logger.error(f"Task step {step.id} execution failed: {e}")
            execution.step_results[step.id] = {"success": False, "error": str(e)}
            return False

    async def _execute_condition_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a condition step"""

        if step.condition:
            result = await self._evaluate_condition(step.condition, execution)
            execution.step_results[step.id] = {"condition_result": result}
            return result

        return True

    async def _execute_loop_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a loop step"""

        try:
            iteration = 0
            loop_results = []

            # Loop over items if specified
            if step.loop_items:
                for item in step.loop_items:
                    if iteration >= step.max_iterations:
                        break

                    # Set loop variable
                    if step.loop_variable:
                        execution.variables[step.loop_variable] = WorkflowVariable(
                            name=step.loop_variable,
                            value=item,
                            type=type(item).__name__
                        )

                    # Execute loop body (would need to be defined in step parameters)
                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            # Loop with condition
            elif step.loop_condition:
                while iteration < step.max_iterations:
                    if not await self._evaluate_condition(step.loop_condition, execution):
                        break

                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            execution.step_results[step.id] = {
                "iterations": iteration,
                "results": loop_results
            }

            return True

        except Exception as e:
            logger.error(f"Loop step {step.id} execution failed: {e}")
            return False

    async def _execute_loop_iteration(self, step: WorkflowStep, execution: WorkflowExecution, iteration: int) -> Any:
        """Execute a single loop iteration"""

        # This would execute the loop body defined in step parameters
        # For now, return a simple result
        return {
            "iteration": iteration,
            "timestamp": time.time(),
            "success": True
        }

    async def _execute_parallel_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute parallel sub-steps"""

        if not step.parallel_steps:
            return True

        try:
            # Create tasks for parallel steps
            tasks = []
            for parallel_step_id in step.parallel_steps:
                if parallel_step_id in workflow.steps:
                    parallel_step = workflow.steps[parallel_step_id]
                    task = asyncio.create_task(
                        self._execute_step(parallel_step, execution, workflow)
                    )
                    tasks.append((parallel_step_id, task))

            # Wait for completion
            if step.wait_for_all:
                # Wait for all tasks to complete
                results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
                success = all(isinstance(result, bool) and result for result in results)
            else:
                # Wait for first successful completion
                done, pending = await asyncio.wait(
                    [task for _, task in tasks],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Cancel pending tasks
                for task in pending:
                    task.cancel()

                # Check if any task succeeded
                success = any(task.result() for task in done if not task.exception())

            execution.step_results[step.id] = {
                "parallel_steps": step.parallel_steps,
                "wait_for_all": step.wait_for_all,
                "success": success
            }

            return success

        except Exception as e:
            logger.error(f"Parallel step {step.id} execution failed: {e}")
            return False

    async def _execute_agent_assignment_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute agent assignment step"""

        try:
            # This step type is used to dynamically assign agents to subsequent steps
            agent_id = await self._assign_agent_to_step(step, execution)

            if agent_id:
                # Store agent assignment for future steps
                target_steps = step.parameters.get("target_steps", [])
                for target_step_id in target_steps:
                    execution.agent_assignments[target_step_id] = agent_id

                execution.step_results[step.id] = {
                    "assigned_agent": agent_id,
                    "target_steps": target_steps
                }
                return True

            return False

        except Exception as e:
            logger.error(f"Agent assignment step {step.id} failed: {e}")
            return False

    async def _execute_pheromone_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute pheromone drop step"""

        try:
            signal_type = step.parameters.get("signal_type", "custom")
            payload = step.parameters.get("payload", {})

            # Add execution context to payload
            payload.update({
                "step_id": step.id,
                "execution_id": execution.id,
                "timestamp": time.time()
            })

            await self._drop_pheromone(signal_type, payload, execution.project_id)

            execution.step_results[step.id] = {
                "signal_type": signal_type,
                "payload": payload
            }

            return True

        except Exception as e:
            logger.error(f"Pheromone step {step.id} failed: {e}")
            return False

    async def _execute_wait_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute wait step"""

        try:
            wait_time = step.parameters.get("wait_time", 1.0)
            wait_condition = step.parameters.get("wait_condition")
            max_wait_time = step.parameters.get("max_wait_time", 300.0)  # 5 minutes default

            start_time = time.time()

            if wait_condition:
                # Wait for condition to be true
                while time.time() - start_time < max_wait_time:
                    # Parse and evaluate wait condition
                    condition = StepCondition(
                        variable=wait_condition.get("variable", ""),
                        operator=ConditionOperator(wait_condition.get("operator", "exists")),
                        value=wait_condition.get("value", True)
                    )

                    if await self._evaluate_condition(condition, execution):
                        break

                    await asyncio.sleep(0.5)  # Check every 500ms
                else:
                    # Timeout
                    logger.warning(f"Wait step {step.id} timed out after {max_wait_time}s")
            else:
                # Simple time-based wait
                await asyncio.sleep(wait_time)

            execution.step_results[step.id] = {
                "wait_time": wait_time,
                "actual_wait_time": time.time() - start_time
            }

            return True

        except Exception as e:
            logger.error(f"Wait step {step.id} failed: {e}")
            return False

    async def _execute_custom_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute custom step type"""

        try:
            # Custom steps can be extended by subclassing
            # For now, treat as a basic task
            return await self._execute_task_step(step, execution, workflow)

        except Exception as e:
            logger.error(f"Custom step {step.id} failed: {e}")
            return False

    async def _assign_agent_to_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Optional[str]:
        """Assign an agent to a step based on requirements"""

        # Check if agent is already assigned
        if step.assigned_agent:
            return step.assigned_agent

        # Check if agent was assigned by previous step
        if step.id in execution.agent_assignments:
            return execution.agent_assignments[step.id]

        # Use agent requirements to find suitable agent
        if step.agent_requirements:
            return await self._find_suitable_agent(step.agent_requirements, execution)

        # Fallback to any available agent
        return await self._get_any_available_agent(execution)

    async def _find_suitable_agent(
        self,
        requirements: AgentRequirement,
        execution: WorkflowExecution
    ) -> Optional[str]:
        """Find an agent that meets the requirements"""

        # Get available agents from agent team
        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if not available_agents:
            logger.warning("No agents available in agent team")
            return None

        # Filter agents based on requirements
        suitable_agents = []

        for agent in available_agents:
            agent_id = agent.get("id", "")
            agent_capabilities = set(agent.get("capabilities", []))
            required_capabilities = set(requirements.capabilities)

            # Check capabilities
            if required_capabilities and not required_capabilities.issubset(agent_capabilities):
                continue

            # Check excluded agents
            if agent_id in requirements.excluded_agents:
                continue

            # Check agent load
            current_load = execution.agent_loads.get(agent_id, 0.0)
            if current_load > requirements.max_load:
                continue

            suitable_agents.append(agent)

        if not suitable_agents:
            logger.warning(f"No suitable agents found for requirements: {requirements}")
            return None

        # Select agent based on strategy
        selected_agent = await self._select_agent_by_strategy(
            suitable_agents,
            requirements.selection_strategy,
            execution
        )

        if selected_agent:
            agent_id = selected_agent.get("id", "")
            # Update agent load
            execution.agent_loads[agent_id] = execution.agent_loads.get(agent_id, 0.0) + 0.1
            return agent_id

        return None

    async def _select_agent_by_strategy(
        self,
        agents: List[Dict[str, Any]],
        strategy: AgentSelectionStrategy,
        execution: WorkflowExecution
    ) -> Optional[Dict[str, Any]]:
        """Select agent based on selection strategy"""

        if not agents:
            return None

        if strategy == AgentSelectionStrategy.ROUND_ROBIN:
            # Simple round-robin selection
            total_assignments = sum(1 for agent_id in execution.agent_assignments.values())
            return agents[total_assignments % len(agents)]

        elif strategy == AgentSelectionStrategy.LEAST_LOADED:
            # Select agent with lowest current load
            min_load = float('inf')
            selected_agent = None

            for agent in agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent = agent

            return selected_agent

        elif strategy == AgentSelectionStrategy.RANDOM:
            import random
            return random.choice(agents)

        elif strategy == AgentSelectionStrategy.PRIORITY_BASED:
            # Select agent with highest priority
            return max(agents, key=lambda a: a.get("priority", 0))

        else:  # CAPABILITY_MATCH or default
            # Select first suitable agent
            return agents[0]

    async def _get_any_available_agent(self, execution: WorkflowExecution) -> Optional[str]:
        """Get any available agent as fallback"""

        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if available_agents:
            # Return least loaded agent
            min_load = float('inf')
            selected_agent_id = None

            for agent in available_agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent_id = agent_id

            return selected_agent_id

        return None

    async def _execute_step_with_agent(self, agent_id: str, step_context: Dict[str, Any]) -> Any:
        """Execute step through assigned agent"""

        try:
            # This would integrate with the actual agent execution system
            # For now, simulate agent execution

            logger.info(f"Executing step {step_context['step_id']} with agent {agent_id}")

            # Simulate processing time
            await asyncio.sleep(0.1)

            # Return fallback success result
            return {
                "success": True,
                "agent_id": agent_id,
                "step_id": step_context["step_id"],
                "result": "Step executed successfully",
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Agent {agent_id} failed to execute step: {e}")
            return {
                "success": False,
                "agent_id": agent_id,
                "error": str(e),
                "timestamp": time.time()
            }

    async def _handle_step_outputs(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Handle step output variables"""

        if not step.output_variables:
            return

        step_result = execution.step_results.get(step.id, {})

        for var_name, result_key in step.output_variables.items():
            if result_key in step_result:
                value = step_result[result_key]
                execution.variables[var_name] = WorkflowVariable(
                    name=var_name,
                    value=value,
                    type=type(value).__name__,
                    description=f"Output from step {step.id}"
                )

    async def _drop_pheromone(self, signal_type: str, payload: Dict[str, Any], project_id: str) -> None:
        """Drop pheromone signal"""

        if self.pheromone_bus:
            try:
                # Use pheromone bus if available
                await self.pheromone_bus.drop_pheromone(
                    signal=signal_type,
                    payload=payload,
                    project_id=project_id
                )
            except Exception as e:
                logger.warning(f"Failed to drop pheromone: {e}")
        else:
            # Log pheromone for debugging
            logger.info(f"Pheromone: {signal_type} - {payload}")

    # Public API methods

    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get execution status by ID"""
        return self.active_executions.get(execution_id)

    def get_all_executions(self) -> List[WorkflowExecution]:
        """Get all active executions"""
        return list(self.active_executions.values())

    async def pause_execution(self, execution_id: str) -> bool:
        """Pause workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.RUNNING:
            execution.status = WorkflowStatus.PAUSED
            return True
        return False

    async def resume_execution(self, execution_id: str) -> bool:
        """Resume paused workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.PAUSED:
            execution.status = WorkflowStatus.RUNNING
            return True
        return False

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at
            return True
        return False

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return {
            **self.execution_stats,
            "active_executions": len(self.active_executions),
            "agent_registry_size": len(self.agent_registry)
        }

# Global workflow engine instance
_global_engine: Optional[WorkflowExecutionEngine] = None

def get_workflow_engine() -> WorkflowExecutionEngine:
    """Get the global workflow engine instance"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine()
    return _global_engine

async def initialize_workflow_engine(pheromone_bus=None) -> WorkflowExecutionEngine:
    """Initialize the global workflow engine"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine(pheromone_bus)
    return _global_engine

# Convenience functions for backward compatibility

# Legacy WorkflowEngine is defined later in the file to maintain compatibility

# Legacy functions for backward compatibility

def load_workflow_definitions(workflows_dir: str = "workflows") -> Dict[str, WorkflowDefinition]:
    """Load all workflow definitions from directory"""
    workflows = {}
    workflows_path = Path(workflows_dir)

    if workflows_path.exists():
        parser = WorkflowYAMLParser()

        for yaml_file in workflows_path.glob("*.yaml"):
            try:
                workflow = parser.parse_workflow_file(yaml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yaml_file}: {e}")

        for yml_file in workflows_path.glob("*.yml"):
            try:
                workflow = parser.parse_workflow_file(yml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yml_file}: {e}")

    return workflows

def get_workflow_for_project_type(project_type: str) -> str:
    """Get appropriate workflow ID for project type"""
    workflow_mapping = {
        "fullstack": "greenfield-fullstack",
        "frontend": "greenfield-fullstack",
        "backend": "greenfield-fullstack",
        "mobile": "greenfield-fullstack",
        "api": "greenfield-fullstack",
        "enhancement": "enhancement",
        "bugfix": "debugging",
        "testing": "testing"
    }

    return workflow_mapping.get(project_type, "greenfield-fullstack")

async def execute_bmad_workflow(
    workflow_type: str,
    project_context: Dict[str, Any],
    agent_team: Dict[str, Any],
    pheromone_bus: Dict[str, Any]
) -> WorkflowExecution:
    """Execute BMAD workflow with full context"""

    engine = get_workflow_engine()

    return await engine.start_workflow(
        workflow_id=workflow_type,
        project_id=project_context.get("project_id", "unknown"),
        agent_team=agent_team,
        pheromone_bus=pheromone_bus,
        context=project_context
    )

# Export main classes and functions
__all__ = [
    # Core classes
    'WorkflowDefinition',
    'WorkflowExecution',
    'WorkflowStep',
    'WorkflowVariable',
    'WorkflowYAMLParser',
    'WorkflowExecutionEngine',

    # Enums
    'WorkflowStatus',
    'StepStatus',
    'StepType',
    'ConditionOperator',
    'AgentSelectionStrategy',

    # Data classes
    'StepCondition',
    'AgentRequirement',
    'RetryConfig',
    'TimeoutConfig',

    # Exceptions
    'WorkflowValidationError',
    'WorkflowExecutionError',
    'WorkflowTimeoutError',
    'AgentAssignmentError',

    # Global functions
    'get_workflow_engine',
    'initialize_workflow_engine',
    'load_workflow_definitions',
    'get_workflow_for_project_type',
    'execute_bmad_workflow',

    # Legacy compatibility
    'WorkflowEngine',
    'WorkflowPhase',  # Legacy
    'PhaseStatus'     # Legacy
]

class WorkflowEngine:
    """BMAD Workflow Engine for orchestrating multi-phase project generation"""
    
    def __init__(self):
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.bmad_path = Path("BMAD-METHOD-main/BMAD-METHOD-main")
        self._load_bmad_workflows()
    
    def _load_bmad_workflows(self):
        """Load BMAD workflow definitions from YAML files"""
        try:
            workflows_dir = self.bmad_path / "bmad-core" / "workflows"
            if not workflows_dir.exists():
                logger.warning(f"BMAD workflows directory not found: {workflows_dir}")
                self._create_default_workflows()
                return
            
            for workflow_file in workflows_dir.glob("*.yml"):
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = yaml.safe_load(f)
                    
                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                    self.workflows[workflow_def.id] = workflow_def
                    logger.info(f"Loaded workflow: {workflow_def.name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load workflow {workflow_file}: {e}")
            
            # Load expansion pack workflows
            expansion_dir = self.bmad_path / "expansion-packs"
            if expansion_dir.exists():
                for pack_dir in expansion_dir.iterdir():
                    if pack_dir.is_dir():
                        pack_workflows = pack_dir / "workflows"
                        if pack_workflows.exists():
                            for workflow_file in pack_workflows.glob("*.yml"):
                                try:
                                    with open(workflow_file, 'r', encoding='utf-8') as f:
                                        workflow_data = yaml.safe_load(f)
                                    
                                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                                    self.workflows[workflow_def.id] = workflow_def
                                    logger.info(f"Loaded expansion workflow: {workflow_def.name}")
                                    
                                except Exception as e:
                                    logger.error(f"Failed to load expansion workflow {workflow_file}: {e}")
            
            logger.info(f"Loaded {len(self.workflows)} BMAD workflows")
            
        except Exception as e:
            logger.error(f"Failed to load BMAD workflows: {e}")
            self._create_default_workflows()
    
    def _parse_workflow_yaml(self, data: Dict[str, Any], file_id: str) -> WorkflowDefinition:
        """Parse YAML workflow data into WorkflowDefinition"""
        
        workflow_info = data.get('workflow', {})
        
        # Parse phases from sequence
        phases = []
        sequence = workflow_info.get('sequence', [])
        
        for i, step in enumerate(sequence):
            if isinstance(step, dict):
                # Handle different step formats
                if 'agent' in step:
                    phase = WorkflowPhase(
                        id=f"phase_{i+1}",
                        name=step.get('name', f"Phase {i+1}"),
                        description=step.get('notes', ''),
                        agent=step['agent'],
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', ''),
                        condition=step.get('condition')
                    )
                elif 'step' in step:
                    # Handle step-based format (like game workflows)
                    phase = WorkflowPhase(
                        id=step.get('step', f"phase_{i+1}"),
                        name=step.get('step', f"Phase {i+1}").replace('_', ' ').title(),
                        description=step.get('notes', ''),
                        agent=step.get('agent', 'developer'),
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', '')
                    )
                else:
                    continue
                
                phases.append(phase)
        
        # Calculate total duration
        total_duration = sum(phase.duration_minutes for phase in phases)
        
        return WorkflowDefinition(
            id=workflow_info.get('id', file_id),
            name=workflow_info.get('name', file_id.replace('-', ' ').title()),
            version=workflow_info.get('version', '1.0.0'),  # Add default version
            description=workflow_info.get('description', ''),
            type=workflow_info.get('type', 'greenfield'),
            project_types=workflow_info.get('project_types', []),
            phases=phases,
            total_duration_minutes=total_duration,
            flow_diagram=workflow_info.get('flow_diagram', ''),
            metadata=workflow_info
        )
    
    def _parse_duration(self, duration_str: str) -> int:
        """Parse duration string to minutes"""
        if isinstance(duration_str, int):
            return duration_str
        
        duration_str = str(duration_str).lower()
        
        if 'hour' in duration_str:
            try:
                hours = float(duration_str.split()[0])
                return int(hours * 60)
            except:
                return 60
        elif 'minute' in duration_str:
            try:
                minutes = float(duration_str.split()[0].split('-')[0])
                return int(minutes)
            except:
                return 30
        else:
            return 30
    
    def _create_default_workflows(self):
        """Create default workflows when BMAD files are not available"""
        
        # Greenfield Full Stack Workflow
        fullstack_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze requirements and create user stories", "analyst", ["project-brief.md"], [], [], 30),
            WorkflowPhase("product_planning", "Product Planning", "Create comprehensive PRD", "pm", ["prd.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["prd.md"], ["user_research_prompt"], 60),
            WorkflowPhase("architecture_design", "Architecture Design", "Design system architecture", "architect", ["fullstack-architecture.md"], ["prd.md", "front-end-spec.md"], ["technical_research_prompt"], 45),
            WorkflowPhase("development", "Development", "Implement the application", "developer", ["source_code"], ["fullstack-architecture.md"], [], 120),
            WorkflowPhase("testing", "Testing & QA", "Test and validate implementation", "qa", ["test_results"], ["source_code"], [], 45),
            WorkflowPhase("documentation", "Documentation", "Create final documentation", "analyst", ["documentation"], ["source_code"], [], 30)
        ]
        
        self.workflows["greenfield-fullstack"] = WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Full Stack Development",
            description="Complete full-stack application development from concept to deployment",
            type="greenfield",
            project_types=["fullstack", "web-app"],
            phases=fullstack_phases,
            total_duration_minutes=sum(p.duration_minutes for p in fullstack_phases)
        )
        
        # Frontend-only workflow
        frontend_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze UI requirements", "analyst", ["project-brief.md"], [], [], 20),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("frontend_development", "Frontend Development", "Implement frontend application", "frontend-developer", ["frontend_code"], ["front-end-spec.md"], [], 90),
            WorkflowPhase("testing", "Testing", "Test frontend functionality", "qa", ["test_results"], ["frontend_code"], [], 30)
        ]
        
        self.workflows["greenfield-frontend"] = WorkflowDefinition(
            id="greenfield-frontend",
            name="Greenfield Frontend Development",
            description="Frontend-only application development",
            type="greenfield",
            project_types=["frontend", "spa"],
            phases=frontend_phases,
            total_duration_minutes=sum(p.duration_minutes for p in frontend_phases)
        )
        
        logger.info("Created default workflows")
    
    async def start_workflow(self, workflow_id: str, project_id: str, agent_team: Dict[str, Any], 
                           pheromone_bus: Dict[str, Any], context: Dict[str, Any]) -> WorkflowExecution:
        """Start a new workflow execution"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            project_id=project_id,
            status=WorkflowStatus.IN_PROGRESS,
            start_time=datetime.now(),
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )
        
        self.executions[project_id] = execution
        
        # Drop pheromone for workflow start
        await self._drop_pheromone("workflow_started", {
            "workflow_id": workflow_id,
            "project_id": project_id,
            "total_phases": len(self.workflows[workflow_id].phases)
        }, project_id)
        
        logger.info(f"Started workflow {workflow_id} for project {project_id}")
        
        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, context))
        
        return execution
    
    async def _execute_workflow(self, execution: WorkflowExecution, context: Dict[str, Any]):
        """Execute workflow phases sequentially"""
        
        workflow = self.workflows[execution.workflow_id]
        
        try:
            for i, phase in enumerate(workflow.phases):
                execution.current_phase_index = i
                execution.progress = i / len(workflow.phases)
                
                # Check if phase should be skipped based on condition
                if phase.condition and not self._evaluate_condition(phase.condition, execution.phase_results):
                    phase.status = PhaseStatus.SKIPPED
                    logger.info(f"Skipping phase {phase.name} due to condition: {phase.condition}")
                    continue
                
                # Execute phase
                phase.status = PhaseStatus.IN_PROGRESS
                phase.start_time = datetime.now()
                
                await self._drop_pheromone("phase_started", {
                    "phase_id": phase.id,
                    "phase_name": phase.name,
                    "agent": phase.agent,
                    "estimated_duration": phase.duration_minutes
                }, execution.project_id)
                
                try:
                    phase_result = await self._execute_phase(phase, execution, context)
                    execution.phase_results[phase.id] = phase_result
                    
                    phase.status = PhaseStatus.COMPLETED
                    phase.end_time = datetime.now()
                    phase.outputs = phase_result.get("outputs", [])
                    
                    await self._drop_pheromone("phase_completed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "success": phase_result.get("success", False),
                        "outputs": phase.outputs,
                        "duration_minutes": (phase.end_time - phase.start_time).total_seconds() / 60
                    }, execution.project_id)
                    
                except Exception as e:
                    phase.status = PhaseStatus.FAILED
                    phase.end_time = datetime.now()
                    execution.error_message = str(e)
                    
                    await self._drop_pheromone("phase_failed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "error": str(e)
                    }, execution.project_id)
                    
                    logger.error(f"Phase {phase.name} failed: {e}")
                    break
            
            # Complete workflow
            execution.status = WorkflowStatus.COMPLETED
            execution.end_time = datetime.now()
            execution.progress = 1.0
            
            await self._drop_pheromone("workflow_completed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "total_duration_minutes": (execution.end_time - execution.start_time).total_seconds() / 60,
                "phases_completed": sum(1 for phase in workflow.phases if phase.status == PhaseStatus.COMPLETED)
            }, execution.project_id)
            
            logger.info(f"Workflow {execution.workflow_id} completed for project {execution.project_id}")
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.end_time = datetime.now()
            execution.error_message = str(e)
            
            await self._drop_pheromone("workflow_failed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "error": str(e)
            }, execution.project_id)
            
            logger.error(f"Workflow {execution.workflow_id} failed: {e}")
    
    async def _execute_phase(self, phase: WorkflowPhase, execution: WorkflowExecution, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow phase"""
        
        # Import agent execution here to avoid circular imports
        try:
            from .agent_executors import create_agent_executor
            
            # Create agent executor for this phase
            agent_executor = create_agent_executor(phase.agent)
            
            # Prepare phase context
            phase_context = {
                **context,
                "phase": phase.id,
                "phase_name": phase.name,
                "creates": phase.creates,
                "requires": phase.requires,
                "optional_steps": phase.optional_steps,
                "previous_results": execution.phase_results
            }
            
            # Execute the phase
            result = await agent_executor.execute(phase_context)
            
            return result
            
        except ImportError:
            # Fallback to basic execution
            logger.warning(f"Agent executors not available, using fallback execution for phase {phase.name}")
            
            await asyncio.sleep(1)  # Simulate work
            
            return {
                "success": True,
                "outputs": phase.creates,
                "message": f"Fallback execution of {phase.name} completed",
                "files_created": len(phase.creates)
            }
    
    def _evaluate_condition(self, condition: str, phase_results: Dict[str, Any]) -> bool:
        """Evaluate a phase condition"""
        # Simple condition evaluation - can be extended
        if condition == "architecture_suggests_prd_changes":
            arch_result = phase_results.get("architecture_design", {})
            return arch_result.get("suggests_prd_changes", False)
        
        return True  # Default to true for unknown conditions
    
    async def _drop_pheromone(self, signal: str, payload: Dict[str, Any], project_id: str):
        """Drop a pheromone signal"""
        try:
            from .pheromone_system import get_pheromone_system
            pheromone_system = get_pheromone_system()
            await pheromone_system.drop_pheromone(signal, payload, project_id)
        except Exception as e:
            logger.warning(f"Failed to drop pheromone: {e}")
    
    def get_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get workflow definition by ID"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[WorkflowDefinition]:
        """List all available workflows"""
        return list(self.workflows.values())
    
    def get_execution(self, project_id: str) -> Optional[WorkflowExecution]:
        """Get workflow execution by project ID"""
        return self.executions.get(project_id)
    
    def get_workflows_for_project_type(self, project_type: str) -> List[WorkflowDefinition]:
        """Get workflows suitable for a project type"""
        return [w for w in self.workflows.values() if project_type in w.project_types or not w.project_types]

    def get_workflow_types(self) -> List[str]:
        """Get available workflow types"""
        return [
            "greenfield-fullstack",
            "enhancement",
            "debugging",
            "testing"
        ]

# Global workflow engine instance
_workflow_engine = None

def get_workflow_engine() -> WorkflowEngine:
    """Get the global workflow engine instance"""
    global _workflow_engine
    if _workflow_engine is None:
        _workflow_engine = WorkflowEngine()
    return _workflow_engine
