"""
Comprehensive Monitoring and Observability System for Aetherforge
Provides health checks, performance metrics, component status tracking, and activity logging
"""

import asyncio
import time
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Setup logging
logger = logging.getLogger(__name__)

@dataclass
class HealthStatus:
    """Health status for a component"""
    component: str
    status: str  # healthy, degraded, unhealthy
    last_check: str
    response_time_ms: float
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class PerformanceMetrics:
    """Performance metrics for system monitoring"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    active_processes: int
    response_times: Dict[str, float]
    error_rates: Dict[str, float]

@dataclass
class ComponentStatus:
    """Status tracking for individual components"""
    component_id: str
    component_type: str
    status: str
    last_activity: str
    uptime_seconds: float
    request_count: int
    error_count: int
    average_response_time: float
    metadata: Dict[str, Any]

class MonitoringSystem:
    """Comprehensive monitoring and observability system"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self.health_checks = {}
        self.performance_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.component_statuses = {}
        self.activity_log = deque(maxlen=10000)  # Keep last 10000 activities
        self.alert_thresholds = self.config.get("alert_thresholds", {})
        self.monitoring_enabled = True
        self.last_health_check = None
        
        # Performance tracking
        self.request_times = defaultdict(list)
        self.error_counts = defaultdict(int)
        self.component_start_times = {}
        
        # Start background monitoring
        self._start_background_monitoring()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load monitoring configuration"""
        default_config = {
            "health_check_interval": 30,  # seconds
            "metrics_collection_interval": 10,  # seconds
            "alert_thresholds": {
                "cpu_percent": 80,
                "memory_percent": 85,
                "disk_percent": 90,
                "response_time_ms": 5000,
                "error_rate_percent": 5
            },
            "retention_days": 7,
            "components_to_monitor": [
                "orchestrator",
                "pheromone_bus",
                "agent_manager",
                "project_manager",
                "workflow_engine"
            ]
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                logger.warning(f"Failed to load monitoring config: {e}")
        
        return default_config
    
    def _start_background_monitoring(self):
        """Start background monitoring tasks"""
        asyncio.create_task(self._periodic_health_checks())
        asyncio.create_task(self._periodic_metrics_collection())
        asyncio.create_task(self._cleanup_old_data())
    
    async def _periodic_health_checks(self):
        """Perform periodic health checks"""
        while self.monitoring_enabled:
            try:
                await self.perform_health_checks()
                await asyncio.sleep(self.config["health_check_interval"])
            except Exception as e:
                logger.error(f"Error in periodic health checks: {e}")
                await asyncio.sleep(5)  # Short delay before retry
    
    async def _periodic_metrics_collection(self):
        """Collect performance metrics periodically"""
        while self.monitoring_enabled:
            try:
                await self.collect_performance_metrics()
                await asyncio.sleep(self.config["metrics_collection_interval"])
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
                await asyncio.sleep(5)
    
    async def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        while self.monitoring_enabled:
            try:
                cutoff_time = datetime.now() - timedelta(days=self.config["retention_days"])
                
                # Clean performance history
                self.performance_history = deque(
                    [m for m in self.performance_history 
                     if datetime.fromisoformat(m.timestamp) > cutoff_time],
                    maxlen=1000
                )
                
                # Clean activity log
                self.activity_log = deque(
                    [a for a in self.activity_log 
                     if datetime.fromisoformat(a["timestamp"]) > cutoff_time],
                    maxlen=10000
                )
                
                await asyncio.sleep(3600)  # Clean every hour
            except Exception as e:
                logger.error(f"Error in data cleanup: {e}")
                await asyncio.sleep(300)  # Retry in 5 minutes
    
    async def register_component(self, component_id: str, component_type: str, 
                                health_check_func: Optional[callable] = None):
        """Register a component for monitoring"""
        self.component_statuses[component_id] = ComponentStatus(
            component_id=component_id,
            component_type=component_type,
            status="starting",
            last_activity=datetime.now().isoformat(),
            uptime_seconds=0,
            request_count=0,
            error_count=0,
            average_response_time=0.0,
            metadata={}
        )
        
        if health_check_func:
            self.health_checks[component_id] = health_check_func
        
        self.component_start_times[component_id] = time.time()
        
        await self.log_activity("component_registered", {
            "component_id": component_id,
            "component_type": component_type
        })
    
    async def update_component_status(self, component_id: str, status: str, 
                                    metadata: Optional[Dict[str, Any]] = None):
        """Update component status"""
        if component_id in self.component_statuses:
            component = self.component_statuses[component_id]
            component.status = status
            component.last_activity = datetime.now().isoformat()
            
            if component_id in self.component_start_times:
                component.uptime_seconds = time.time() - self.component_start_times[component_id]
            
            if metadata:
                component.metadata.update(metadata)
            
            await self.log_activity("component_status_updated", {
                "component_id": component_id,
                "status": status,
                "metadata": metadata
            })
    
    async def record_request(self, component_id: str, response_time_ms: float, 
                           success: bool = True):
        """Record a request for performance tracking"""
        if component_id in self.component_statuses:
            component = self.component_statuses[component_id]
            component.request_count += 1
            component.last_activity = datetime.now().isoformat()
            
            if not success:
                component.error_count += 1
            
            # Update average response time
            self.request_times[component_id].append(response_time_ms)
            if len(self.request_times[component_id]) > 100:  # Keep last 100 requests
                self.request_times[component_id].pop(0)
            
            component.average_response_time = sum(self.request_times[component_id]) / len(self.request_times[component_id])
    
    async def perform_health_checks(self) -> Dict[str, HealthStatus]:
        """Perform health checks on all registered components"""
        health_results = {}
        
        for component_id, health_check_func in self.health_checks.items():
            start_time = time.time()
            
            try:
                if asyncio.iscoroutinefunction(health_check_func):
                    result = await health_check_func()
                else:
                    result = health_check_func()
                
                response_time = (time.time() - start_time) * 1000
                
                health_results[component_id] = HealthStatus(
                    component=component_id,
                    status="healthy" if result.get("healthy", True) else "unhealthy",
                    last_check=datetime.now().isoformat(),
                    response_time_ms=response_time,
                    error_message=result.get("error"),
                    metadata=result.get("metadata", {})
                )
                
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                health_results[component_id] = HealthStatus(
                    component=component_id,
                    status="unhealthy",
                    last_check=datetime.now().isoformat(),
                    response_time_ms=response_time,
                    error_message=str(e)
                )
        
        self.last_health_check = health_results
        return health_results
    
    async def collect_performance_metrics(self) -> PerformanceMetrics:
        """Collect system performance metrics"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            # Component response times
            response_times = {}
            for component_id in self.component_statuses:
                if component_id in self.request_times and self.request_times[component_id]:
                    response_times[component_id] = sum(self.request_times[component_id]) / len(self.request_times[component_id])
            
            # Error rates
            error_rates = {}
            for component_id, component in self.component_statuses.items():
                if component.request_count > 0:
                    error_rates[component_id] = (component.error_count / component.request_count) * 100
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=(disk.used / disk.total) * 100,
                network_io={
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv
                },
                active_processes=len(psutil.pids()),
                response_times=response_times,
                error_rates=error_rates
            )
            
            self.performance_history.append(metrics)
            
            # Check for alerts
            await self._check_alert_thresholds(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect performance metrics: {e}")
            return None
    
    async def _check_alert_thresholds(self, metrics: PerformanceMetrics):
        """Check if any metrics exceed alert thresholds"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds.get("cpu_percent", 80):
            alerts.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.alert_thresholds.get("memory_percent", 85):
            alerts.append(f"High memory usage: {metrics.memory_percent:.1f}%")
        
        if metrics.disk_percent > self.alert_thresholds.get("disk_percent", 90):
            alerts.append(f"High disk usage: {metrics.disk_percent:.1f}%")
        
        for component, response_time in metrics.response_times.items():
            if response_time > self.alert_thresholds.get("response_time_ms", 5000):
                alerts.append(f"Slow response time for {component}: {response_time:.1f}ms")
        
        for component, error_rate in metrics.error_rates.items():
            if error_rate > self.alert_thresholds.get("error_rate_percent", 5):
                alerts.append(f"High error rate for {component}: {error_rate:.1f}%")
        
        if alerts:
            await self.log_activity("alerts_triggered", {
                "alerts": alerts,
                "metrics": asdict(metrics)
            })
    
    async def log_activity(self, activity_type: str, data: Dict[str, Any]):
        """Log system activity"""
        activity = {
            "timestamp": datetime.now().isoformat(),
            "activity_type": activity_type,
            "data": data
        }
        
        self.activity_log.append(activity)
        logger.info(f"Activity logged: {activity_type}")
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview"""
        latest_metrics = self.performance_history[-1] if self.performance_history else None
        
        return {
            "system_health": {
                "overall_status": self._calculate_overall_health(),
                "last_health_check": self.last_health_check,
                "components_monitored": len(self.component_statuses)
            },
            "performance": {
                "latest_metrics": asdict(latest_metrics) if latest_metrics else None,
                "metrics_history_count": len(self.performance_history)
            },
            "components": {
                component_id: asdict(status) 
                for component_id, status in self.component_statuses.items()
            },
            "activity": {
                "recent_activities": list(self.activity_log)[-10:],  # Last 10 activities
                "total_activities": len(self.activity_log)
            },
            "monitoring_config": self.config
        }
    
    def _calculate_overall_health(self) -> str:
        """Calculate overall system health status"""
        if not self.last_health_check:
            return "unknown"
        
        healthy_count = sum(1 for status in self.last_health_check.values() if status.status == "healthy")
        total_count = len(self.last_health_check)
        
        if total_count == 0:
            return "unknown"
        
        health_percentage = (healthy_count / total_count) * 100
        
        if health_percentage >= 90:
            return "healthy"
        elif health_percentage >= 70:
            return "degraded"
        else:
            return "unhealthy"

# Global monitoring instance
monitoring_system = MonitoringSystem()

# FastAPI router for monitoring endpoints
monitoring_router = APIRouter(prefix="/monitoring", tags=["monitoring"])

@monitoring_router.get("/health")
async def get_health_status():
    """Get current health status of all components"""
    try:
        health_status = await monitoring_system.perform_health_checks()
        return {
            "status": "success",
            "health_checks": {k: asdict(v) for k, v in health_status.items()},
            "overall_health": monitoring_system._calculate_overall_health()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@monitoring_router.get("/metrics")
async def get_performance_metrics():
    """Get current performance metrics"""
    try:
        latest_metrics = monitoring_system.performance_history[-1] if monitoring_system.performance_history else None
        return {
            "status": "success",
            "latest_metrics": asdict(latest_metrics) if latest_metrics else None,
            "metrics_count": len(monitoring_system.performance_history)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Metrics collection failed: {str(e)}")

@monitoring_router.get("/overview")
async def get_system_overview():
    """Get comprehensive system overview"""
    try:
        overview = await monitoring_system.get_system_overview()
        return {"status": "success", "overview": overview}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Overview generation failed: {str(e)}")

@monitoring_router.get("/components")
async def get_component_statuses():
    """Get status of all monitored components"""
    try:
        return {
            "status": "success",
            "components": {
                component_id: asdict(status) 
                for component_id, status in monitoring_system.component_statuses.items()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Component status retrieval failed: {str(e)}")

@monitoring_router.get("/activities")
async def get_recent_activities(limit: int = 50):
    """Get recent system activities"""
    try:
        activities = list(monitoring_system.activity_log)[-limit:]
        return {
            "status": "success",
            "activities": activities,
            "total_count": len(monitoring_system.activity_log)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Activity retrieval failed: {str(e)}")
