import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock VS Code API
const mockVSCode = {
  postMessage: vi.fn(),
  sendRequest: vi.fn(),
  getState: vi.fn(),
  setState: vi.fn()
};

vi.mock('@/utils/vscode', () => ({
  vscode: mockVSCode,
  useVSCodeMessage: vi.fn((type, handler) => {
    // Mock implementation for useVSCodeMessage hook
    return handler;
  })
}));

// Mock D3 and vis-network
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({ remove: vi.fn() })),
    append: vi.fn(() => ({
      selectAll: vi.fn(() => ({
        data: vi.fn(() => ({
          enter: vi.fn(() => ({
            append: vi.fn(() => ({
              attr: vi.fn(() => ({ attr: vi.fn() })),
              style: vi.fn(() => ({ style: vi.fn() })),
              text: vi.fn(() => ({ text: vi.fn() })),
              on: vi.fn(() => ({ on: vi.fn() }))
            }))
          }))
        }))
      }))
    })),
    attr: vi.fn(() => ({ attr: vi.fn() })),
    call: vi.fn()
  })),
  forceSimulation: vi.fn(() => ({
    force: vi.fn(() => ({ force: vi.fn() })),
    on: vi.fn(),
    stop: vi.fn()
  })),
  zoom: vi.fn(() => ({
    scaleExtent: vi.fn(() => ({ on: vi.fn() }))
  }))
}));

vi.mock('vis-network', () => ({
  Network: vi.fn(() => ({
    on: vi.fn(),
    destroy: vi.fn(),
    fit: vi.fn(),
    setOptions: vi.fn()
  }))
}));

vi.mock('vis-data', () => ({
  DataSet: vi.fn((data) => ({
    length: data?.length || 0,
    ...data
  }))
}));

// Import components after mocking
import ProjectStructureVisualizer from '@/components/ProjectStructureVisualizer';
import CodeQualityVisualizer from '@/components/CodeQualityVisualizer';
import CodeQualityHeatMap from '@/components/CodeQualityHeatMap';
import EnhancedWorkflowVisualization from '@/components/EnhancedWorkflowVisualization';
import VisualizationControls from '@/components/VisualizationControls';
import RealTimeVisualizationUpdates from '@/components/RealTimeVisualizationUpdates';
import VirtualizedVisualization from '@/components/VirtualizedVisualization';
import ResponsiveVisualizationLayout from '@/components/ResponsiveVisualizationLayout';
import ComprehensiveVisualizationDashboard from '@/components/ComprehensiveVisualizationDashboard';

// Mock data
const mockProjectStructure = {
  id: 'test-project',
  name: 'Test Project',
  files: [
    {
      id: 'file-1',
      name: 'index.ts',
      path: '/src/index.ts',
      type: 'file',
      size: 1024,
      testCoverage: 85,
      issues: []
    },
    {
      id: 'file-2',
      name: 'components',
      path: '/src/components',
      type: 'directory',
      children: [
        {
          id: 'file-3',
          name: 'Button.tsx',
          path: '/src/components/Button.tsx',
          type: 'file',
          size: 512,
          testCoverage: 95,
          issues: []
        }
      ]
    }
  ],
  components: [
    {
      id: 'comp-1',
      name: 'Button Component',
      type: 'ui',
      status: 'completed',
      description: 'Reusable button component',
      files: ['file-3'],
      technologies: ['React', 'TypeScript'],
      metrics: {
        testCoverage: 95,
        complexity: 2.5
      }
    }
  ],
  dependencies: {
    nodes: [
      { id: 'file-1', name: 'index.ts', type: 'file' },
      { id: 'file-3', name: 'Button.tsx', type: 'file' }
    ],
    edges: [
      { from: 'file-1', to: 'file-3', type: 'import' }
    ]
  },
  metrics: {
    totalFiles: 2,
    totalComponents: 1,
    averageComplexity: 2.5,
    overallTestCoverage: 90
  }
};

const mockQualityMetrics = {
  overall: 85,
  categories: [
    { name: 'Maintainability', score: 80, weight: 0.3 },
    { name: 'Reliability', score: 90, weight: 0.3 },
    { name: 'Security', score: 85, weight: 0.2 },
    { name: 'Performance', score: 88, weight: 0.2 }
  ],
  issues: [
    {
      category: 'style',
      severity: 'medium',
      count: 5,
      issues: [
        {
          id: 'issue-1',
          message: 'Missing semicolon',
          category: 'style',
          severity: 'medium',
          line: 10,
          column: 5
        }
      ]
    }
  ],
  recommendations: [
    {
      id: 'rec-1',
      title: 'Improve test coverage',
      description: 'Add unit tests for utility functions',
      priority: 'high',
      impact: 'medium'
    }
  ],
  trends: []
};

const mockWorkflowExecution = {
  id: 'workflow-1',
  name: 'Test Workflow',
  status: 'running',
  steps: [
    {
      id: 'step-1',
      name: 'Analysis',
      type: 'analysis',
      status: 'completed',
      progress: 1,
      assignedAgent: 'analyst'
    },
    {
      id: 'step-2',
      name: 'Development',
      type: 'development',
      status: 'running',
      progress: 0.6,
      assignedAgent: 'developer'
    }
  ]
};

describe('Visualization Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockVSCode.sendRequest.mockResolvedValue({});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('ProjectStructureVisualizer', () => {
    it('renders project structure correctly', async () => {
      render(
        <ProjectStructureVisualizer
          projectId="test-project"
          structure={mockProjectStructure}
        />
      );

      expect(screen.getByText('Project Structure')).toBeInTheDocument();
      expect(screen.getByText('(2 files, 1 components)')).toBeInTheDocument();
    });

    it('handles file selection', async () => {
      const onFileSelect = vi.fn();
      render(
        <ProjectStructureVisualizer
          projectId="test-project"
          structure={mockProjectStructure}
          onFileSelect={onFileSelect}
        />
      );

      const fileElement = screen.getByText('index.ts');
      fireEvent.click(fileElement);

      expect(onFileSelect).toHaveBeenCalledWith(mockProjectStructure.files[0]);
    });

    it('toggles view modes', async () => {
      render(
        <ProjectStructureVisualizer
          projectId="test-project"
          structure={mockProjectStructure}
        />
      );

      const viewSelector = screen.getByDisplayValue('Tree View');
      fireEvent.change(viewSelector, { target: { value: 'graph' } });

      await waitFor(() => {
        expect(viewSelector).toHaveValue('graph');
      });
    });
  });

  describe('CodeQualityVisualizer', () => {
    it('renders quality metrics correctly', () => {
      render(
        <CodeQualityVisualizer
          projectId="test-project"
          metrics={mockQualityMetrics}
        />
      );

      expect(screen.getByText('Code Quality')).toBeInTheDocument();
      expect(screen.getByText('85% Overall Score')).toBeInTheDocument();
    });

    it('filters issues by severity', async () => {
      render(
        <CodeQualityVisualizer
          projectId="test-project"
          metrics={mockQualityMetrics}
        />
      );

      const severityFilter = screen.getByDisplayValue('All Severities');
      fireEvent.change(severityFilter, { target: { value: 'medium' } });

      await waitFor(() => {
        expect(severityFilter).toHaveValue('medium');
      });
    });

    it('handles issue selection', async () => {
      const onIssueSelect = vi.fn();
      render(
        <CodeQualityVisualizer
          projectId="test-project"
          metrics={mockQualityMetrics}
          onIssueSelect={onIssueSelect}
        />
      );

      // Switch to issues view
      const viewSelector = screen.getByDisplayValue('Overview');
      fireEvent.change(viewSelector, { target: { value: 'issues' } });

      await waitFor(() => {
        const issueElement = screen.getByText('Style Issues');
        fireEvent.click(issueElement);
      });
    });
  });

  describe('CodeQualityHeatMap', () => {
    it('renders heat map with files', () => {
      render(
        <CodeQualityHeatMap
          projectId="test-project"
          files={mockProjectStructure.files}
          qualityMetrics={mockQualityMetrics}
        />
      );

      expect(screen.getByText('Code Quality Heat Map')).toBeInTheDocument();
    });

    it('changes metric visualization', async () => {
      render(
        <CodeQualityHeatMap
          projectId="test-project"
          files={mockProjectStructure.files}
          qualityMetrics={mockQualityMetrics}
        />
      );

      const metricSelector = screen.getByDisplayValue('Complexity');
      fireEvent.change(metricSelector, { target: { value: 'coverage' } });

      await waitFor(() => {
        expect(metricSelector).toHaveValue('coverage');
      });
    });
  });

  describe('EnhancedWorkflowVisualization', () => {
    it('renders workflow execution', () => {
      render(
        <EnhancedWorkflowVisualization
          projectId="test-project"
          execution={mockWorkflowExecution}
        />
      );

      expect(screen.getByText('Workflow Visualization')).toBeInTheDocument();
      expect(screen.getByText('2 steps')).toBeInTheDocument();
    });

    it('handles play/pause controls', async () => {
      render(
        <EnhancedWorkflowVisualization
          projectId="test-project"
          execution={mockWorkflowExecution}
        />
      );

      const playButton = screen.getByText('Play');
      fireEvent.click(playButton);

      await waitFor(() => {
        expect(screen.getByText('Pause')).toBeInTheDocument();
      });
    });
  });

  describe('VisualizationControls', () => {
    it('handles search input', async () => {
      const onSearch = vi.fn();
      render(
        <VisualizationControls onSearch={onSearch} />
      );

      const searchInput = screen.getByPlaceholderText('Search and navigate...');
      await userEvent.type(searchInput, 'test query');

      expect(onSearch).toHaveBeenCalledWith('test query');
    });

    it('toggles filters panel', async () => {
      render(<VisualizationControls />);

      const filtersButton = screen.getByText('Filters');
      fireEvent.click(filtersButton);

      await waitFor(() => {
        expect(screen.getByText('Advanced Filters')).toBeInTheDocument();
      });
    });

    it('handles export functionality', async () => {
      const onExport = vi.fn();
      render(<VisualizationControls onExport={onExport} />);

      const exportButton = screen.getByText('Export');
      fireEvent.click(exportButton);

      await waitFor(() => {
        const pngOption = screen.getByText('PNG Image');
        fireEvent.click(pngOption);
        expect(onExport).toHaveBeenCalledWith('image');
      });
    });
  });

  describe('RealTimeVisualizationUpdates', () => {
    it('renders update controls', () => {
      render(
        <RealTimeVisualizationUpdates projectId="test-project" />
      );

      expect(screen.getByText('Real-time Updates')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });

    it('toggles active state', async () => {
      render(
        <RealTimeVisualizationUpdates projectId="test-project" />
      );

      const activeButton = screen.getByText('Active');
      fireEvent.click(activeButton);

      await waitFor(() => {
        expect(screen.getByText('Paused')).toBeInTheDocument();
      });
    });
  });

  describe('VirtualizedVisualization', () => {
    it('renders items efficiently', () => {
      const items = Array.from({ length: 1000 }, (_, i) => ({
        id: `item-${i}`,
        name: `Item ${i}`,
        type: 'file' as const
      }));

      render(
        <VirtualizedVisualization
          items={items}
          enableVirtualization={true}
          renderThreshold={100}
        />
      );

      // Should not render all 1000 items at once
      const renderedItems = screen.getAllByText(/Item \d+/);
      expect(renderedItems.length).toBeLessThan(1000);
    });

    it('handles search filtering', async () => {
      const items = [
        { id: '1', name: 'Component A', type: 'file' as const },
        { id: '2', name: 'Component B', type: 'file' as const },
        { id: '3', name: 'Service X', type: 'file' as const }
      ];

      render(
        <VirtualizedVisualization
          items={items}
          searchQuery="Component"
        />
      );

      expect(screen.getByText('Component A')).toBeInTheDocument();
      expect(screen.getByText('Component B')).toBeInTheDocument();
      expect(screen.queryByText('Service X')).not.toBeInTheDocument();
    });
  });

  describe('ResponsiveVisualizationLayout', () => {
    it('adapts to viewport changes', async () => {
      // Mock window dimensions
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 640
      });

      const onLayoutChange = vi.fn();
      render(
        <ResponsiveVisualizationLayout onLayoutChange={onLayoutChange}>
          <div>Child 1</div>
          <div>Child 2</div>
        </ResponsiveVisualizationLayout>
      );

      // Trigger resize event
      act(() => {
        window.dispatchEvent(new Event('resize'));
      });

      await waitFor(() => {
        expect(onLayoutChange).toHaveBeenCalled();
      });
    });

    it('shows layout controls', async () => {
      render(
        <ResponsiveVisualizationLayout>
          <div>Child</div>
        </ResponsiveVisualizationLayout>
      );

      const settingsButton = screen.getByRole('button');
      fireEvent.click(settingsButton);

      await waitFor(() => {
        expect(screen.getByText('Layout Configuration')).toBeInTheDocument();
      });
    });
  });

  describe('ComprehensiveVisualizationDashboard', () => {
    beforeEach(() => {
      mockVSCode.sendRequest.mockImplementation((type) => {
        switch (type) {
          case 'getProjectStructure':
            return Promise.resolve(mockProjectStructure);
          case 'getQualityMetrics':
            return Promise.resolve(mockQualityMetrics);
          case 'getWorkflowExecutions':
            return Promise.resolve([mockWorkflowExecution]);
          default:
            return Promise.resolve({});
        }
      });
    });

    it('loads and displays project data', async () => {
      render(
        <ComprehensiveVisualizationDashboard projectId="test-project" />
      );

      await waitFor(() => {
        expect(screen.getByText('Project Visualization')).toBeInTheDocument();
      });

      expect(mockVSCode.sendRequest).toHaveBeenCalledWith('getProjectStructure', { projectId: 'test-project' });
      expect(mockVSCode.sendRequest).toHaveBeenCalledWith('getQualityMetrics', { projectId: 'test-project' });
      expect(mockVSCode.sendRequest).toHaveBeenCalledWith('getWorkflowExecutions', { projectId: 'test-project' });
    });

    it('switches between visualization modes', async () => {
      render(
        <ComprehensiveVisualizationDashboard projectId="test-project" />
      );

      await waitFor(() => {
        expect(screen.getByText('Project Visualization')).toBeInTheDocument();
      });

      const structureButton = screen.getByText('Structure');
      fireEvent.click(structureButton);

      await waitFor(() => {
        expect(structureButton).toHaveClass('bg-white text-blue-600');
      });
    });

    it('handles loading states', () => {
      mockVSCode.sendRequest.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(
        <ComprehensiveVisualizationDashboard projectId="test-project" />
      );

      expect(screen.getByText('Loading visualization data...')).toBeInTheDocument();
    });

    it('handles error states', async () => {
      mockVSCode.sendRequest.mockRejectedValue(new Error('Failed to load data'));

      render(
        <ComprehensiveVisualizationDashboard projectId="test-project" />
      );

      await waitFor(() => {
        expect(screen.getByText('Failed to load data')).toBeInTheDocument();
      });
    });
  });
});

// Performance Tests
describe('Performance Tests', () => {
  it('handles large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
      id: `item-${i}`,
      name: `Item ${i}`,
      type: 'file' as const,
      path: `/path/to/item-${i}`,
      size: Math.random() * 10000
    }));

    const startTime = performance.now();
    
    render(
      <VirtualizedVisualization
        items={largeDataset}
        enableVirtualization={true}
        renderThreshold={100}
      />
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render large datasets quickly (under 100ms)
    expect(renderTime).toBeLessThan(100);
  });

  it('maintains smooth animations with many elements', async () => {
    const manyItems = Array.from({ length: 500 }, (_, i) => ({
      id: `item-${i}`,
      name: `Item ${i}`,
      type: 'file' as const
    }));

    const { rerender } = render(
      <VirtualizedVisualization items={manyItems.slice(0, 100)} />
    );

    const startTime = performance.now();
    
    // Simulate adding more items
    rerender(
      <VirtualizedVisualization items={manyItems} />
    );

    const endTime = performance.now();
    const updateTime = endTime - startTime;

    // Updates should be fast even with many items
    expect(updateTime).toBeLessThan(50);
  });
});

// Accessibility Tests
describe('Accessibility Tests', () => {
  it('provides proper ARIA labels', () => {
    render(
      <ProjectStructureVisualizer
        projectId="test-project"
        structure={mockProjectStructure}
      />
    );

    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveAttribute('aria-label');
    });
  });

  it('supports keyboard navigation', async () => {
    render(
      <VisualizationControls />
    );

    const searchInput = screen.getByPlaceholderText('Search and navigate...');
    searchInput.focus();

    await userEvent.keyboard('{Tab}');
    
    const filtersButton = screen.getByText('Filters');
    expect(filtersButton).toHaveFocus();
  });

  it('maintains focus management', async () => {
    render(
      <ComprehensiveVisualizationDashboard projectId="test-project" />
    );

    await waitFor(() => {
      expect(screen.getByText('Project Visualization')).toBeInTheDocument();
    });

    // Test focus trap in modals/panels
    const settingsButton = screen.getByText('Sidebar');
    fireEvent.click(settingsButton);

    // Focus should be managed properly
    expect(document.activeElement).toBeTruthy();
  });
});
